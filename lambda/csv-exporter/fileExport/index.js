const AWS = require("aws-sdk");
const fs = require("fs");
const csv = require("csv-parser");
const createCsvWriter = require("csv-writer").createObjectCsvWriter;
const S3 = new AWS.S3();
const filePath = process.env.CSV_FILE_PATH;
const { GetObjectCommand, S3Client } = require("@aws-sdk/client-s3");
const { getSignedUrl } = require("@aws-sdk/s3-request-presigner");
const exportToCsvAndS3 = async (records, fileConfig) => {
  const fileName = `${fileConfig.fileName}.csv`;
  let filteredRecords = records;

  const csvWriter = createCsvWriter({
    path: `${filePath}/${fileName}`,
    header: fileConfig.fields,
  });

  if (fileConfig.filter) {
    filteredRecords = records.filter((r) => fileConfig.filter(r));
  }

  // const csvData = csvjson.toCSV(filteredRecords, { headers: fileConfig.fields });

  await csvWriter.writeRecords(filteredRecords);

  // Upload to S3 bucket
  const data = await fs.readFileSync(`${filePath}/${fileName}`);

  const result = await S3.upload({
    Bucket: process.env.S3_BUCKET_NAME,
    Key: fileName,
    Body: data,
  }).promise();

  let presignedUrl = "";

  // try {
  //   presignedUrl = await S3.getSignedUrl("getObject", {
  //     Bucket: process.env.S3_BUCKET_NAME,
  //     Key: `${fileName}`,
  //     Expires: parseInt(process.env.PRESIGNED_URL_EXPIRATION), // Set the expiration time for the URL in seconds
  //   });
  // } catch (error) {
  //   console.log(JSON.stringify(error));
  // }

  try {
    const maxExpireTime = process.env.PRESIGNED_URL_EXPIRATION;
    const region = "us-west-2";

    const accessKeyId = process.env.S3_PRESIGNED_URL_ACCESS_KEY_ID;
    const secretAccessKey = process.env.S3_PRESIGNED_URL_SECRET_ACCESS_KEY;

    const s3client = new S3Client({
      region,
      credentials: {
        accessKeyId,
        secretAccessKey,
      },
      signatureVersion: "v4",
    });

    const getObjectCommand = new GetObjectCommand({
      Bucket: process.env.S3_BUCKET_NAME,
      Key: `${fileName}`,
    });
    presignedUrl = await getSignedUrl(s3client, getObjectCommand, {
      expiresIn: maxExpireTime,
    });
  } catch (error) {
    console.log(JSON.stringify(error));
  }

  return {
    // fileLink: result.Location,
    fileLink: presignedUrl,
    email: fileConfig.email,
    subject: fileConfig.subject,
  };
};

const getExistingRecords = async (fileConfig) => {
  const fileName = `${fileConfig.fileName}.csv`;
  const data = [];
  return new Promise((resolve, reject) => {
    fs.createReadStream(`${filePath}/${fileName}`)
      .pipe(csv())
      .on("data", function (row) {
        data.push(row);
      })
      .on("end", function () {
        resolve(data);
      })
      .on("error", function (error) {
        reject(error);
      });
  });
};

const downloadFromS3 = async (fileConfig) => {
  const fileName = `${fileConfig.fileName}.csv`;

  const writeStream = fs.createWriteStream(`${filePath}/${fileName}`, {
    encoding: "utf-8",
    flags: "a",
  });

  const params = {
    Bucket: process.env.S3_BUCKET_NAME,
    Key: fileName,
  };

  return new Promise((resolve, reject) => {
    const readStream = S3.getObject(params).createReadStream();

    // Error handling in read stream
    readStream.on("error", async (e) => {
      if (e.code === "NoSuchKey") {
        // If file not found create a blank file with headers
        const csvWriter = createCsvWriter({
          path: `${filePath}/${fileName}`,
          header: fileConfig.fields,
        });

        await csvWriter.writeRecords([]);
        resolve(fileName);
      }
      reject(e);
    });

    // Resolve only if we are done writing
    writeStream.once("finish", () => {
      console.log("Finished writing", fileName);
      resolve(fileName);
    });

    // pipe will automatically finish the write stream once done
    readStream.pipe(writeStream);
  });
};

module.exports = { exportToCsvAndS3, downloadFromS3, getExistingRecords };
