// Load the AWS SDK for Node.js
const AWS = require("aws-sdk");
const SES = new AWS.SES();
const { REGISTRATION_OPTIONS, MARKET_OPTIONS, BUSINESS_INTEREST_OPTIONS, CERTIFICATIONS_OPTIONS, EMPLOYMENT_INTEREST_OPTIONS, FILES, COMPANY_DESCRIPTION_OPTIONS } = require("../../constants");
const sendEmail = async (data) => {
  let toAddresses = data.email;
  // Removed Business Opportunities for as per client request.
  // if (data?.fileLink?.indexOf("nto-business-inquiries") > 0) {
  //   toAddresses = process.env.NTO_EMAILS
  // }
  const params = {
    Destination: {
      ToAddresses: toAddresses.split(","),
    },
    Message: {
      Body: {
        Html: {
          Charset: "UTF-8",
          Data: `<html>
                    <head></head>
                    <body>
                      <p>Dear <PERSON>,</p>
                      <p>Please find the inquiries at <a href=${data.fileLink}>${data.fileLink}</a> </p>
                      <br/>
                      <p>Regards,</p>
                      <p>URW Airports Team </p>
                    </body>
                    </html>`,
        },
      },
      Subject: {
        Charset: "UTF-8",
        Data: data.subject,
      },
    },
    Source: process.env.SOURCE_EMAIL,
  };

  // Create the promise and SES service object
  const result = await SES.sendEmail(params).promise();

  return result;
};

const sendConnectFormEmail = async (data) => {
  const toAddresses = process.env.CONTACT_US_FORM_SUBMISSION_EMAILS;

  const params = {
    Destination: {
      ToAddresses: toAddresses ? toAddresses.split(",") : [],
    },
    Message: {
      Body: {
        Html: {
          Charset: "UTF-8",
          Data: `<html>
                    <head></head>
                    <body>
                    <p style="margin:0in;font-size:12pt;font-family:Calibri,sans-serif">Below are the details for connect form submission</p>
                    <br><br>
                    <table border="0" cellspacing="5" cellpadding="0">
                        <tbody>
                            <tr>
                            <td valign="top" style="padding: 0.75pt">
                                <div style="margin: 0in; font-size: 11pt; font-family: Calibri, sans-serif">FIRST NAME:<u></u><u></u></div>
                            </td>
                            <td style="padding: 0.75pt">
                                <div style="margin: 0in; font-size: 11pt; font-family: Calibri, sans-serif">${data.firstName}<u></u><u></u></div>
                            </td>
                            </tr>
                            <tr>
                            <td valign="top" style="padding: 0.75pt">
                                <div style="margin: 0in; font-size: 11pt; font-family: Calibri, sans-serif">LAST NAME:<u></u><u></u></div>
                            </td>
                            <td style="padding: 0.75pt">
                                <div style="margin: 0in; font-size: 11pt; font-family: Calibri, sans-serif">${data.lastName}<u></u><u></u></div>
                            </td>
                            </tr>
                            <tr>
                            <td valign="top" style="padding: 0.75pt">
                                <div style="margin: 0in; font-size: 11pt; font-family: Calibri, sans-serif">EMAIL:<u></u><u></u></div>
                            </td>
                            <td style="padding: 0.75pt">
                                <div style="margin: 0in; font-size: 11pt; font-family: Calibri, sans-serif">
                                <a href="mailto:${data.email}" style="color: blue; text-decoration: underline" target="_blank">${data.email}</a><u></u><u></u>
                                </div>
                            </td>
                            </tr>
                            <tr>
                            <td valign="top" style="padding: 0.75pt">
                                <div style="margin: 0in; font-size: 11pt; font-family: Calibri, sans-serif">COMPANY / ORGANIZATION:<u></u><u></u></div>
                            </td>
                            <td style="padding: 0.75pt">
                                <div style="margin: 0in; font-size: 11pt; font-family: Calibri, sans-serif">${data.company}<u></u><u></u></div>
                            </td>
                            </tr>
                            <tr>
                            <td valign="top" style="padding: 0.75pt">
                                <div style="margin: 0in; font-size: 11pt; font-family: Calibri, sans-serif">PHONE:<u></u><u></u></div>
                            </td>
                            <td style="padding: 0.75pt">
                                <div style="margin: 0in; font-size: 11pt; font-family: Calibri, sans-serif">${data.mobile}<u></u><u></u></div>
                            </td>
                            </tr>
                            <tr>
                            <td valign="top" style="padding: 0.75pt">
                                <div style="margin: 0in; font-size: 11pt; font-family: Calibri, sans-serif">YOUR MESSAGE:<u></u><u></u></div>
                            </td>
                            <td style="padding: 0.75pt">
                                <div style="margin: 0in; font-size: 11pt; font-family: Calibri, sans-serif">
                                ${data.message}
                                </div>
                            </td>
                            </tr>
                            <tr>
                            <td valign="top" style="padding: 0.75pt">
                                <div style="margin: 0in; font-size: 11pt; font-family: Calibri, sans-serif">
                                I'D LIKE TO RECEIVE OCCASIONAL EMAIL UPDATES FROM URW AIRPORTS, <br>
                                INCLUDING INFORMATION ABOUT ADVANCE NETWORK NEWS AND OPPORTUNITIES.&nbsp;&nbsp;
                                <u></u><u></u></div>
                            </td>
                            <td style="padding: 0.75pt">
                                <div style="margin: 0in; font-size: 11pt; font-family: Calibri, sans-serif">${data.emailUpdates ? "Yes" : "No"}<u></u><u></u></div>
                            </td>
                            </tr>
                        </tbody>
                        </table>
                    </body>
                    </html>`,
        },
      },
      Subject: {
        Charset: "UTF-8",
        Data: "Connect form submission",
      },
    },
    Source: process.env.SOURCE_EMAIL,
  };

  // Create the promise and SES service object
  const result = await SES.sendEmail(params).promise();

  return result;
};

const sendRegisterNowFormEmail = async (data) => {
  var businessOpportunities = Object.entries(data)
    .filter((x) => x[0].startsWith("bi") && x[1])
    .map((item) => {
      return REGISTRATION_OPTIONS.find((x) => x.key == item[0]).value;
    })
    .join(", ");

  var marketInterests = Object.entries(data)
    .filter((x) => x[0].startsWith("mi") && x[1])
    .map((item) => {
      return MARKET_OPTIONS.find((x) => x.key == item[0]).value;
    })
    .join(", ");

  var companyDescriptions = Object.entries(data)
    .filter((x) => x[0].startsWith("cd") && x[1])
    .map((item) => {
      return COMPANY_DESCRIPTION_OPTIONS.find((x) => x.key == item[0]).value;
    })
    .join(", ");
  const toAddresses = process.env.REGISTER_NOW_FORM_SUBMISSION_EMAILS;

  console.log(toAddresses);
  const params = {
    Destination: {
      ToAddresses: toAddresses ? toAddresses.split(",") : []
    },
    Message: {
      Body: {
        Html: {
          Charset: "UTF-8",
          Data: `<html>
                    <head></head>
                    <body>
                      <p style="margin:0in;font-size:12pt;font-family:Calibri,sans-serif">Below are the details for Register now form submission</p>
                      <br><br>
                      <table border="0" cellspacing="5" cellpadding="0">
                        <tbody>
                          <tr>
                            <td valign="top" style="padding: 0.75pt">
                              <div style="margin: 0in; font-size: 11pt; font-family: Calibri, sans-serif">FIRST NAME:<u></u><u></u></div>
                            </td>
                            <td style="padding: 0.75pt">
                              <div style="margin: 0in; font-size: 11pt; font-family: Calibri, sans-serif">${data.firstName}<u></u><u></u></div>
                            </td>
                          </tr>
                          <tr>
                            <td valign="top" style="padding: 0.75pt">
                              <div style="margin: 0in; font-size: 11pt; font-family: Calibri, sans-serif">LAST NAME:<u></u><u></u></div>
                            </td>
                            <td style="padding: 0.75pt">
                              <div style="margin: 0in; font-size: 11pt; font-family: Calibri, sans-serif">${data.lastName}<u></u><u></u></div>
                            </td>
                          </tr>
                          <tr>
                            <td valign="top" style="padding: 0.75pt">
                              <div style="margin: 0in; font-size: 11pt; font-family: Calibri, sans-serif">EMAIL:<u></u><u></u></div>
                            </td>
                            <td style="padding: 0.75pt">
                              <div style="margin: 0in; font-size: 11pt; font-family: Calibri, sans-serif">
                                <a href="mailto:${data.email}" style="color: blue; text-decoration: underline" target="_blank">${data.email}</a><u></u><u></u>
                              </div>
                            </td>
                          </tr>
                          <tr>
                            <td valign="top" style="padding: 0.75pt">
                              <div style="margin: 0in; font-size: 11pt; font-family: Calibri, sans-serif">COMPANY / ORGANIZATION:<u></u><u></u></div>
                            </td>
                            <td style="padding: 0.75pt">
                              <div style="margin: 0in; font-size: 11pt; font-family: Calibri, sans-serif">${data.company}<u></u><u></u></div>
                            </td>
                          </tr>
                          <tr>
                            <td valign="top" style="padding: 0.75pt">
                              <div style="margin: 0in; font-size: 11pt; font-family: Calibri, sans-serif">PHONE:<u></u><u></u></div>
                            </td>
                            <td style="padding: 0.75pt">
                              <div style="margin: 0in; font-size: 11pt; font-family: Calibri, sans-serif">${data.mobile}<u></u><u></u></div>
                            </td>
                          </tr>
                          <tr>
                            <td valign="top" style="padding: 0.75pt">
                              <div style="margin: 0in; font-size: 11pt; font-family: Calibri, sans-serif">WEBSITE:<u></u><u></u></div>
                            </td>
                            <td style="padding: 0.75pt">
                              <div style="margin: 0in; font-size: 11pt; font-family: Calibri, sans-serif">${data.website}</div>
                            </td>
                          </tr>
                          <tr>
                            <td valign="top" style="padding: 0.75pt">
                              <div style="margin: 0in; font-size: 11pt; font-family: Calibri, sans-serif">I AM INTERESTED IN BUSINESS OPPORTUNITIES IN :<u></u><u></u></div>
                            </td>
                            <td style="padding: 0.75pt">
                              <div style="margin: 0in; font-size: 11pt; font-family: Calibri, sans-serif">${businessOpportunities}<u></u><u></u></div>
                            </td>
                          </tr>
                          <tr>
                            <td valign="top" style="padding: 0.75pt">
                              <div style="margin: 0in; font-size: 11pt; font-family: Calibri, sans-serif">IS THERE A SPECIFIC MARKET YOU'RE INTERESTED IN? :<u></u><u></u></div>
                            </td>
                            <td style="padding: 0.75pt">
                              <div style="margin: 0in; font-size: 11pt; font-family: Calibri, sans-serif">${marketInterests}<u></u><u></u></div>
                            </td>
                          </tr>
                           <tr>
                            <td valign="top" style="padding: 0.75pt">
                              <div style="margin: 0in; font-size: 11pt; font-family: Calibri, sans-serif">WHAT BEST DESCRIBES YOUR COMPANY? :<u></u><u></u></div>
                            </td>
                            <td style="padding: 0.75pt">
                              <div style="margin: 0in; font-size: 11pt; font-family: Calibri, sans-serif">${companyDescriptions}<u></u><u></u></div>
                            </td>
                          </tr>
                          <tr>
                            <td valign="top" style="padding: 0.75pt">
                              <div style="margin: 0in; font-size: 11pt; font-family: Calibri, sans-serif">ANYTHING ELSE YOU WOULD LIKE US TO KNOW? :<u></u><u></u></div>
                            </td>
                            <td style="padding: 0.75pt">
                              <div style="margin: 0in; font-size: 11pt; font-family: Calibri, sans-serif">${data.otherInformation}<u></u><u></u></div>
                            </td>
                          </tr>
                          <tr>
                            <td valign="top" style="padding: 0.75pt">
                              <div style="margin: 0in; font-size: 11pt; font-family: Calibri, sans-serif">
                              I'D LIKE TO RECEIVE OCCASIONAL EMAIL UPDATES FROM URW AIRPORTS, <br>
                               INCLUDING INFORMATION ABOUT ADVANCE NETWORK NEWS AND OPPORTUNITIES.&nbsp;&nbsp;<u></u><u></u>
                              </div>
                            </td>
                            <td style="padding: 0.75pt">
                              <div style="margin: 0in; font-size: 11pt; font-family: Calibri, sans-serif">${data.registerNowEmailUpdates ? "Yes" : "No"}<u></u><u></u></div>
                            </td>
                          </tr>
                        </tbody>
                      </table>
                    </body>
                    </html>`,
        },
      },
      Subject: {
        Charset: "UTF-8",
        Data: "Register Now form Submission",
      },
    },
    Source: process.env.SOURCE_EMAIL,
  };

  // Create the promise and SES service object
  const result = await SES.sendEmail(params).promise();

  return result;
};

const sendBusinessOpportunitiesFormEmail = async (data) => {
  var businessInterests = Object.entries(data)
    .filter((x) => x[0].startsWith("bi") && x[1])
    .map((item) => {
      return BUSINESS_INTEREST_OPTIONS.find((x) => x.key == item[0]).value;
    })
    .join(", ");

  var certification = Object.entries(data)
    .filter((x) => x[0].startsWith("cert") && x[1])
    .map((item) => {
      return CERTIFICATIONS_OPTIONS.find((x) => x.key == item[0]).value;
    })
    .join(", ");
  const toAddresses = process.env.TO_EMAILS;

  console.log(toAddresses);
  const params = {
    Destination: {
      ToAddresses: toAddresses ? toAddresses.split(",") : [],
    },
    Message: {
      Body: {
        Html: {
          Charset: "UTF-8",
          Data: `<html>
          <head></head>
          <body>
            <p style="margin: 0in; font-size: 12pt; font-family: Calibri, sans-serif">Below are the details for Business Opportunities form submission</p>
            <br /><br />
            <table border="0" cellspacing="5" cellpadding="0">
              <tbody>
                <tr>
                  <td valign="top" style="padding: 0.75pt">
                    <div style="margin: 0in; font-size: 11pt; font-family: Calibri, sans-serif">FIRST NAME:<u></u><u></u></div>
                  </td>
                  <td style="padding: 0.75pt">
                    <div style="margin: 0in; font-size: 11pt; font-family: Calibri, sans-serif">${data.firstName}<u></u><u></u></div>
                  </td>
                </tr>
                <tr>
                  <td valign="top" style="padding: 0.75pt">
                    <div style="margin: 0in; font-size: 11pt; font-family: Calibri, sans-serif">LAST NAME:<u></u><u></u></div>
                  </td>
                  <td style="padding: 0.75pt">
                    <div style="margin: 0in; font-size: 11pt; font-family: Calibri, sans-serif">${data.lastName}<u></u><u></u></div>
                  </td>
                </tr>
                <tr>
                  <td valign="top" style="padding: 0.75pt">
                    <div style="margin: 0in; font-size: 11pt; font-family: Calibri, sans-serif">EMAIL:<u></u><u></u></div>
                  </td>
                  <td style="padding: 0.75pt">
                    <div style="margin: 0in; font-size: 11pt; font-family: Calibri, sans-serif">
                      <a href="mailto:${data.email}" style="color: blue; text-decoration: underline" target="_blank">${data.email}</a><u></u><u></u>
                    </div>
                  </td>
                </tr>
                <tr>
                  <td valign="top" style="padding: 0.75pt">
                    <div style="margin: 0in; font-size: 11pt; font-family: Calibri, sans-serif">COMPANY / ORGANIZATION:<u></u><u></u></div>
                  </td>
                  <td style="padding: 0.75pt">
                    <div style="margin: 0in; font-size: 11pt; font-family: Calibri, sans-serif">${data.companyName}<u></u><u></u></div>
                  </td>
                </tr>
                <tr>
                  <td valign="top" style="padding: 0.75pt">
                    <div style="margin: 0in; font-size: 11pt; font-family: Calibri, sans-serif">PHONE:<u></u><u></u></div>
                  </td>
                  <td style="padding: 0.75pt">
                    <div style="margin: 0in; font-size: 11pt; font-family: Calibri, sans-serif">${data.mobile}<u></u><u></u></div>
                  </td>
                </tr>
                <tr>
                  <td valign="top" style="padding: 0.75pt">
                    <div style="margin: 0in; font-size: 11pt; font-family: Calibri, sans-serif">Business Address 1:<u></u><u></u></div>
                  </td>
                  <td style="padding: 0.75pt">
                    <div style="margin: 0in; font-size: 11pt; font-family: Calibri, sans-serif">${data.businessAddress1}</div>
                  </td>
                </tr>
                <tr>
                  <td valign="top" style="padding: 0.75pt">
                    <div style="margin: 0in; font-size: 11pt; font-family: Calibri, sans-serif">Business Address 2:<u></u><u></u></div>
                  </td>
                  <td style="padding: 0.75pt">
                    <div style="margin: 0in; font-size: 11pt; font-family: Calibri, sans-serif">${data.businessAddress2}</div>
                  </td>
                </tr>
                <tr>
                  <td valign="top" style="padding: 0.75pt">
                    <div style="margin: 0in; font-size: 11pt; font-family: Calibri, sans-serif">City:<u></u><u></u></div>
                  </td>
                  <td style="padding: 0.75pt">
                    <div style="margin: 0in; font-size: 11pt; font-family: Calibri, sans-serif">${data.city}</div>
                  </td>
                </tr>
                <tr>
                  <td valign="top" style="padding: 0.75pt">
                    <div style="margin: 0in; font-size: 11pt; font-family: Calibri, sans-serif">State:<u></u><u></u></div>
                  </td>
                  <td style="padding: 0.75pt">
                    <div style="margin: 0in; font-size: 11pt; font-family: Calibri, sans-serif">${data.state}</div>
                  </td>
                </tr>
                <tr>
                  <td valign="top" style="padding: 0.75pt">
                    <div style="margin: 0in; font-size: 11pt; font-family: Calibri, sans-serif">Zip Code:<u></u><u></u></div>
                  </td>
                  <td style="padding: 0.75pt">
                    <div style="margin: 0in; font-size: 11pt; font-family: Calibri, sans-serif">${data.zipCode}</div>
                  </td>
                </tr>
                <tr>
                  <td valign="top" style="padding: 0.75pt">
                    <div style="margin: 0in; font-size: 11pt; font-family: Calibri, sans-serif">Website:<u></u><u></u></div>
                  </td>
                  <td style="padding: 0.75pt">
                    <div style="margin: 0in; font-size: 11pt; font-family: Calibri, sans-serif">${data.website}</div>
                  </td>
                </tr>
                <tr>
                  <td valign="top" style="padding: 0.75pt">
                    <div style="margin: 0in; font-size: 11pt; font-family: Calibri, sans-serif">Years in Business:<u></u><u></u></div>
                  </td>
                  <td style="padding: 0.75pt">
                    <div style="margin: 0in; font-size: 11pt; font-family: Calibri, sans-serif">${data.yearsInBusiness}</div>
                  </td>
                </tr>
                <tr>
                  <td valign="top" style="padding: 0.75pt">
                    <div style="margin: 0in; font-size: 11pt; font-family: Calibri, sans-serif">I AM INTERESTED IN BUSINESS OPPORTUNITIES IN :<u></u><u></u></div>
                  </td>
                  <td style="padding: 0.75pt">
                    <div style="margin: 0in; font-size: 11pt; font-family: Calibri, sans-serif">${businessInterests}<u></u><u></u></div>
                  </td>
                </tr>
                <tr>
                  <td valign="top" style="padding: 0.75pt">
                    <div style="margin: 0in; font-size: 11pt; font-family: Calibri, sans-serif">IS YOUR BUSINESS CURRENTLY CERTIFIED IN ANY OF THE FOLLOWING DESIGNATIONS?<u></u><u></u></div>
                  </td>
                  <td style="padding: 0.75pt">
                    <div style="margin: 0in; font-size: 11pt; font-family: Calibri, sans-serif">${certification}<u></u><u></u></div>
                  </td>
                </tr>
                <tr>
                  <td valign="top" style="padding: 0.75pt">
                    <div style="margin: 0in; font-size: 11pt; font-family: Calibri, sans-serif">BRIEFLY DESCRIBE YOUR BUSINESS :<u></u><u></u></div>
                  </td>
                  <td style="padding: 0.75pt">
                    <div style="margin: 0in; font-size: 11pt; font-family: Calibri, sans-serif">${data.aboutBusiness}<u></u><u></u></div>
                  </td>
                </tr>
                <tr>
                  <td valign="top" style="padding: 0.75pt">
                    <div style="margin: 0in; font-size: 11pt; font-family: Calibri, sans-serif">AIRPORT EXPERIENCE :<u></u><u></u></div>
                  </td>
                  <td style="padding: 0.75pt">
                    <div style="margin: 0in; font-size: 11pt; font-family: Calibri, sans-serif">${data.airportExperience}<u></u><u></u></div>
                  </td>
                </tr>
                <tr>
                  <td valign="top" style="padding: 0.75pt">
                    <div style="margin: 0in; font-size: 11pt; font-family: Calibri, sans-serif">EMAIL UPDATES</div>
                  </td>
                  <td style="padding: 0.75pt">
                    <div style="margin: 0in; font-size: 11pt; font-family: Calibri, sans-serif">${data.emailUpdates ? "Yes" : "No"}<u></u><u></u></div>
                  </td>
                </tr>
                <tr>
                  <td valign="top" style="padding: 0.75pt">
                    <div style="margin: 0in; font-size: 11pt; font-family: Calibri, sans-serif">TEXT UPDATES</div>
                  </td>
                  <td style="padding: 0.75pt">
                    <div style="margin: 0in; font-size: 11pt; font-family: Calibri, sans-serif">${data.textUpdates ? "Yes" : "No"}<u></u><u></u></div>
                  </td>
                </tr>
                <tr>
                  <td valign="top" style="padding: 0.75pt">
                    <div style="margin: 0in; font-size: 11pt; font-family: Calibri, sans-serif">ADVANCE NETWORK NEWS</div>
                  </td>
                  <td style="padding: 0.75pt">
                    <div style="margin: 0in; font-size: 11pt; font-family: Calibri, sans-serif">${data.advancedNetworkNews ? "Yes" : "No"}<u></u><u></u></div>
                  </td>
                </tr>
              </tbody>
            </table>
          </body>
        </html>
        `,
        },
      },
      Subject: {
        Charset: "UTF-8",
        Data: "Business Opportunities form Submission",
      },
    },
    Source: process.env.SOURCE_EMAIL,
  };

  // Create the promise and SES service object
  const result = await SES.sendEmail(params).promise();

  return result;
};

const sendEmploymentOpportunitiesFormEmail = async (data) => {
  var employmentOpportunities = Object.entries(data)
    .filter((x) => x[0].startsWith("ei") && x[1])
    .map((item) => {
      return EMPLOYMENT_INTEREST_OPTIONS.find((x) => x.key == item[0]).value;
    })
    .join(", ");

  const toAddresses = process.env.TO_EMAILS;

  console.log(toAddresses);
  const params = {
    Destination: {
      ToAddresses: toAddresses ? toAddresses.split(",") : [],
    },
    Message: {
      Body: {
        Html: {
          Charset: "UTF-8",
          Data: `<html>
          <head></head>
          <body>
            <p style="margin: 0in; font-size: 12pt; font-family: Calibri, sans-serif">Below are the details for Employment Opportunities form submission</p>
            <br /><br />
            <table border="0" cellspacing="5" cellpadding="0">
              <tbody>
                <tr>
                  <td valign="top" style="padding: 0.75pt">
                    <div style="margin: 0in; font-size: 11pt; font-family: Calibri, sans-serif">FIRST NAME:<u></u><u></u></div>
                  </td>
                  <td style="padding: 0.75pt">
                    <div style="margin: 0in; font-size: 11pt; font-family: Calibri, sans-serif">${data.firstName}<u></u><u></u></div>
                  </td>
                </tr>
                <tr>
                  <td valign="top" style="padding: 0.75pt">
                    <div style="margin: 0in; font-size: 11pt; font-family: Calibri, sans-serif">LAST NAME:<u></u><u></u></div>
                  </td>
                  <td style="padding: 0.75pt">
                    <div style="margin: 0in; font-size: 11pt; font-family: Calibri, sans-serif">${data.lastName}<u></u><u></u></div>
                  </td>
                </tr>
                <tr>
                  <td valign="top" style="padding: 0.75pt">
                    <div style="margin: 0in; font-size: 11pt; font-family: Calibri, sans-serif">EMAIL:<u></u><u></u></div>
                  </td>
                  <td style="padding: 0.75pt">
                    <div style="margin: 0in; font-size: 11pt; font-family: Calibri, sans-serif">
                      <a href="mailto:${data.email}" style="color: blue; text-decoration: underline" target="_blank">${data.email}</a><u></u><u></u>
                    </div>
                  </td>
                </tr>
                <tr>
                  <td valign="top" style="padding: 0.75pt">
                    <div style="margin: 0in; font-size: 11pt; font-family: Calibri, sans-serif">PHONE:<u></u><u></u></div>
                  </td>
                  <td style="padding: 0.75pt">
                    <div style="margin: 0in; font-size: 11pt; font-family: Calibri, sans-serif">${data.mobile}<u></u><u></u></div>
                  </td>
                </tr>
                <tr>
                  <td valign="top" style="padding: 0.75pt">
                    <div style="margin: 0in; font-size: 11pt; font-family: Calibri, sans-serif">Home Address 1:<u></u><u></u></div>
                  </td>
                  <td style="padding: 0.75pt">
                    <div style="margin: 0in; font-size: 11pt; font-family: Calibri, sans-serif">${data.homeAddress1}</div>
                  </td>
                </tr>
                <tr>
                  <td valign="top" style="padding: 0.75pt">
                    <div style="margin: 0in; font-size: 11pt; font-family: Calibri, sans-serif">Home Address 2:<u></u><u></u></div>
                  </td>
                  <td style="padding: 0.75pt">
                    <div style="margin: 0in; font-size: 11pt; font-family: Calibri, sans-serif">${data.homeAddress2}</div>
                  </td>
                </tr>
                <tr>
                  <td valign="top" style="padding: 0.75pt">
                    <div style="margin: 0in; font-size: 11pt; font-family: Calibri, sans-serif">City:<u></u><u></u></div>
                  </td>
                  <td style="padding: 0.75pt">
                    <div style="margin: 0in; font-size: 11pt; font-family: Calibri, sans-serif">${data.city}</div>
                  </td>
                </tr>
                <tr>
                  <td valign="top" style="padding: 0.75pt">
                    <div style="margin: 0in; font-size: 11pt; font-family: Calibri, sans-serif">State:<u></u><u></u></div>
                  </td>
                  <td style="padding: 0.75pt">
                    <div style="margin: 0in; font-size: 11pt; font-family: Calibri, sans-serif">${data.state}</div>
                  </td>
                </tr>
                <tr>
                  <td valign="top" style="padding: 0.75pt">
                    <div style="margin: 0in; font-size: 11pt; font-family: Calibri, sans-serif">Zip Code:<u></u><u></u></div>
                  </td>
                  <td style="padding: 0.75pt">
                    <div style="margin: 0in; font-size: 11pt; font-family: Calibri, sans-serif">${data.zipCode}</div>
                  </td>
                </tr>
                <tr>
                  <td valign="top" style="padding: 0.75pt">
                    <div style="margin: 0in; font-size: 11pt; font-family: Calibri, sans-serif">AIRPORT EMPLOYMENT EXPERIENCE:<u></u><u></u></div>
                  </td>
                  <td style="padding: 0.75pt">
                    <div style="margin: 0in; font-size: 11pt; font-family: Calibri, sans-serif">${data.airportEmploymentExperience}</div>
                  </td>
                </tr>
                <tr>
                  <td valign="top" style="padding: 0.75pt">
                    <div style="margin: 0in; font-size: 11pt; font-family: Calibri, sans-serif">I AM INTERESTED IN EMPLOYMENT OPPORTUNITIES IN :<u></u><u></u></div>
                  </td>
                  <td style="padding: 0.75pt">
                    <div style="margin: 0in; font-size: 11pt; font-family: Calibri, sans-serif">${employmentOpportunities}<u></u><u></u></div>
                  </td>
                </tr>
                <tr>
                  <td valign="top" style="padding: 0.75pt">
                    <div style="margin: 0in; font-size: 11pt; font-family: Calibri, sans-serif">EMAIL UPDATES</div>
                  </td>
                  <td style="padding: 0.75pt">
                    <div style="margin: 0in; font-size: 11pt; font-family: Calibri, sans-serif">${data.emailUpdates ? "Yes" : "No"}<u></u><u></u></div>
                  </td>
                </tr>
                <tr>
                  <td valign="top" style="padding: 0.75pt">
                    <div style="margin: 0in; font-size: 11pt; font-family: Calibri, sans-serif">TEXT UPDATES</div>
                  </td>
                  <td style="padding: 0.75pt">
                    <div style="margin: 0in; font-size: 11pt; font-family: Calibri, sans-serif">${data.textUpdates ? "Yes" : "No"}<u></u><u></u></div>
                  </td>
                </tr>
                <tr>
                  <td valign="top" style="padding: 0.75pt">
                    <div style="margin: 0in; font-size: 11pt; font-family: Calibri, sans-serif">ADVANCE NETWORK NEWS</div>
                  </td>
                  <td style="padding: 0.75pt">
                    <div style="margin: 0in; font-size: 11pt; font-family: Calibri, sans-serif">${data.advancedNetworkNews ? "Yes" : "No"}<u></u><u></u></div>
                  </td>
                </tr>
              </tbody>
            </table>
          </body>
        </html>
        `,
        },
      },
      Subject: {
        Charset: "UTF-8",
        Data: "Employment Opportunities form Submission",
      },
    },
    Source: process.env.SOURCE_EMAIL,
  };

  // Create the promise and SES service object
  const result = await SES.sendEmail(params).promise();

  return result;
};

module.exports = { sendEmail, sendConnectFormEmail, sendRegisterNowFormEmail, sendBusinessOpportunitiesFormEmail, sendEmploymentOpportunitiesFormEmail };
