const fetch = require("node-fetch");
class ContentfulApi {
  static async callContentful(query) {
    // const fetchUrl = `https://graphql.contentful.com/content/v1/spaces/${process.env.CONTENTFUL_SPACE_ID}`;
    const fetchUrl = `https://graphql.contentful.com/content/v1/spaces/${process.env.CONTENTFUL_SPACE_ID}/environments/${process.env.CONTENTFUL_ENVIRONMENT}`;

    const fetchOptions = {
      method: "POST",
      headers: {
        Authorization: `Bearer ${process.env.CONTENTFUL_ACCESS_TOKEN}`,
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ query }),
    };

    try {
      const data = await fetch(fetchUrl, fetchOptions).then((response) => response.json());
      return data;
    } catch (error) {
      console.log(error);
      throw new Error("Could not fetch data from Contentful!");
    }
  }

  // Get Latest News For EmailTemplate
  static async GetLatestNewsForEmailTemplates() {
    const query = `{
      newsCollection(order: newsPublishedDate_DESC, limit: 3) {
        items {
          title
          tags
          bannerImage {
            url
          }
          slug
        }
      }
    }`;

    const response = await this.callContentful(query);

    const { newsCollection } = response.data;

    let allNews = [...newsCollection?.items];

    allNews = allNews
      .filter((m) => m != null)
      .map((item) => {
        return {
          title: item?.title,
          tags: item?.tags,
          cardImage: item?.bannerImage?.url,
          link: `${process.env.SITE_URL}/${item?.slug}/`,
        };
      });

    return allNews;
  }
}

module.exports = ContentfulApi;
