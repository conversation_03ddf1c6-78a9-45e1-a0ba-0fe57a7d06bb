const mailchimpTransactional = require("@mailchimp/mailchimp_transactional")(process.env.MAILCHIMP_TRANSACTIONAL_API_KEY);

const sendTemplate = async (subject, templateName, toEmail, vars) => {
  try {
    const response = await mailchimpTransactional.messages.sendTemplate({
      template_name: templateName,
      template_content: [{}],
      message: {
        subject: subject,
        headers: { "Content-Type": "text/html;charset=UTF-8" },
        to: [{ email: toEmail }],
        from_email: process.env.MAILCHIMP_TRANSACTIONAL_FROM_EMAIL,
        merge_vars: [
          {
            merge_language: "handlebars",
            rcpt: toEmail,
            vars: vars,
          },
        ],
      },
    });
    console.log(JSON.stringify(response));
  } catch (error) {
    console.log(JSON.stringify(error));
  }
};

module.exports = { sendTemplate };
