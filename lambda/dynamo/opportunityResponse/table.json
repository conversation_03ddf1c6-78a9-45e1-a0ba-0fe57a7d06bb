{"TableName": "OpportunityResponse", "KeySchema": [{"AttributeName": "propertyId", "KeyType": "HASH"}, {"AttributeName": "responseId", "KeyType": "RANGE"}], "AttributeDefinitions": [{"AttributeName": "propertyId", "AttributeType": "S"}, {"AttributeName": "responseId", "AttributeType": "S"}], "ProvisionedThroughput": {"ReadCapacityUnits": 1, "WriteCapacityUnits": 1}, "GlobalSecondaryIndexes": [{"IndexName": "propertyId-createdAt-index", "KeySchema": [{"AttributeName": "propertyId", "KeyType": "HASH"}, {"AttributeName": "createdAt", "KeyType": "RANGE"}], "Projection": {"ProjectionType": "ALL"}, "ProvisionedThroughput": {"ReadCapacityUnits": 1, "WriteCapacityUnits": 1}}]}