{"TableName": "ContactResponse", "KeySchema": [{"AttributeName": "propertyId", "KeyType": "HASH"}, {"AttributeName": "mobile", "KeyType": "RANGE"}], "AttributeDefinitions": [{"AttributeName": "propertyId", "AttributeType": "S"}, {"AttributeName": "mobile", "AttributeType": "S"}], "ProvisionedThroughput": {"ReadCapacityUnits": 1, "WriteCapacityUnits": 1}, "GlobalSecondaryIndexes": [{"IndexName": "propertyId-createdAt-index", "KeySchema": [{"AttributeName": "propertyId", "KeyType": "HASH"}, {"AttributeName": "createdAt", "KeyType": "RANGE"}], "Projection": {"ProjectionType": "ALL"}, "ProvisionedThroughput": {"ReadCapacityUnits": 1, "WriteCapacityUnits": 1}}]}