const uuid = require("uuid");
const { putRow } = require("../dynamo");
const {
  DYNAMO_TABLES,
  FORM_TYPES,
  URWAIRPORT_EMAIL_TEMPLATES,
} = require("../constants");
const { addMember } = require("../mailchimp");
const ContentfulApi = require("../contentful/contentfulApi");
const { sendTemplate } = require("../mailchimp/templates");
const {
  sendConnectFormEmail,
  sendRegisterNowFormEmail,
  sendBusinessOpportunitiesFormEmail,
  sendEmploymentOpportunitiesFormEmail
} = require("../csv-exporter/ses");
exports.handler = async (event) => {
  let response;
  let statusCode;
  let featuredNews;
  let vars;

  const headers = {
    "Access-Control-Allow-Headers": "*",
    "Access-Control-Allow-Methods": "GET, POST, OPTIONS", // Allow only GET request
  };

  if (process.env.NODE_ENV === "local") {
    headers["Access-Control-Allow-Origin"] = "*";
  }

  if (event.httpMethod === "OPTIONS") {
    return {
      statusCode: 200,
      headers,
    };
  }

  if (!event.body) {
    return {
      statusCode: 400,
      body: JSON.stringify({
        message: "No data provided",
      }),
    };
  }

  const data = JSON.parse(event.body);

  const id = uuid.v4();
  console.log(data.formType);
  console.log(JSON.stringify(data.formData));

  try {
    switch (data.formType) {
      case FORM_TYPES.CONTACT_US:
        await putRow(DYNAMO_TABLES.CONTACT_RESPONSE, data.formData);
        await addMember(data.formData, data.formType);
        await sendConnectFormEmail(data.formData);
        statusCode = 200;
        response = {
          message: "Success",
        };
        break;

      case FORM_TYPES.JFK_CONTACT_US:
        await putRow(DYNAMO_TABLES.CONTACT_RESPONSE, data.formData);
        await addMember(data.formData, data.formType);
        await sendConnectFormEmail(data.formData);
        statusCode = 200;
        response = {
          message: "Success",
        };
        break;

      case FORM_TYPES.STAY_UP_TO_DATE:
        await putRow(DYNAMO_TABLES.OPPORTUNITY_RESPONSE, {
          ...data.formData,
          responseId: id,
          formType: data.formType,
        });
        await addMember(data.formData, data.formType);

        statusCode = 200;
        response = {
          message: "Success",
          responseId: id,
        };
        break;

      case FORM_TYPES.REGISTER_NOW:
        await putRow(DYNAMO_TABLES.OPPORTUNITY_RESPONSE, {
          ...data.formData,
          responseId: id,
          formType: data.formType,
        });
        await addMember(data.formData, data.formType);
        await sendRegisterNowFormEmail(data.formData);

        featuredNews = await ContentfulApi.GetLatestNewsForEmailTemplates();
        vars = [
          {
            name: "featuredNews",
            content: featuredNews,
          },
        ];

        await sendTemplate(
          "URW Airports - You're on our list",
          URWAIRPORT_EMAIL_TEMPLATES.REGISTER_NOW,
          data.formData.email,
          vars
        );

        statusCode = 200;
        response = {
          message: "Success",
        };
        break;

      case FORM_TYPES.BUSINESS_OPPORTUNITY:
        await putRow(DYNAMO_TABLES.OPPORTUNITY_RESPONSE, {
          ...data.formData,
          responseId: id,
          formType: data.formType,
        });
        await addMember(data.formData, data.formType);
        await sendBusinessOpportunitiesFormEmail(data.formData);
        statusCode = 200;
        response = {
          message: "Success",
          responseId: id,
        };
        break;
      case FORM_TYPES.EMPLOYMENT_OPPORTUNITY:
        await putRow(DYNAMO_TABLES.OPPORTUNITY_RESPONSE, {
          ...data.formData,
          responseId: id,
          formType: data.formType,
        });
        await addMember(data.formData, data.formType);
        await sendEmploymentOpportunitiesFormEmail(data.formData);
        statusCode = 200;
        response = {
          message: "Success",
          responseId: id,
        };
        break;

      default:
        statusCode = 400;
        response = {
          message: "Invalid form type provided",
        };
    }
    return {
      statusCode,
      headers,
      body: JSON.stringify(response),
    };
  } catch (error) {
    console.error(JSON.stringify(error));
    return {
      headers,
      statusCode: 400,
      body: JSON.stringify({
        message: error,
      }),
    };
  }
};
