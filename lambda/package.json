{"name": "urw-airports-api", "version": "0.1.0", "private": true, "scripts": {"start-api": "sam local start-api --profile 989597836662_AWSPowerUserAccess --region us-west-2 --port 3001"}, "dependencies": {"@aws-sdk/client-s3": "^3.465.0", "@aws-sdk/s3-request-presigner": "^3.465.0", "@mailchimp/mailchimp_marketing": "^3.0.78", "@mailchimp/mailchimp_transactional": "^1.0.50", "aws-sdk": "^2.1222.0", "crypto": "^1.0.1", "csv-parser": "^3.0.0", "csv-writer": "^1.6.0", "csvjson": "^5.1.0", "dotenv": "^16.0.2", "node-fetch": "^2.6.7", "uuid": "^9.0.0"}}