export const businessIntrestsOptions = [
  {
    key: "bi/architectureDesignEngineering",
    value: "Design",
  },
  { key: "bi/construction", value: "Construction" },
  { key: "bi/concessions", value: "Concessions" },
  { key: "bi/services", value: "Services" },
  { key: "bi/other", value: "Others" },
];
export const certificationOptions = [
  {
    key: "cert/acdbe",
    value: "ACDBE - Airport Concessions Disadvantaged Business Enterprise",
  },
  { key: "cert/lbe", value: "LBE - Local Business Enterprise" },
  { key: "cert/mbe", value: "MBE - Minority Business Enterprise" },
  {
    key: "cert/sdvosb",
    value: "SDVOSB - Service-Disabled Veteran-Owned Small Businesses",
  },
  { key: "cert/wbe", value: "WBE - Women Business Enterprise" },
  { key: "cert/sbe", value: "SBE - Small Business Enterprise" },
  { key: "cert/notCertified", value: "Not Certified" },
  { key: "cert/other", value: "Other" },
];

export const employmentInterestsOptions = [
  { key: "ei/foodAndBeverage", value: "Food & Beverage" },
  { key: "ei/retail", value: "Retail" },
  { key: "ei/construction", value: "Construction" },
  { key: "ei/maintenance", value: "Maintenance" },
  { key: "ei/professionalServices", value: "Professional Services" },
];

export const companyDescriptionOptions = [
  { key: "cd/womanOwned", value: "Woman-owned" },
  { key: "cd/minorityOwned", value: "Minority-owned" },
  { key: "cd/veteranOwned", value: "Veteran-owned" },
  { key: "cd/acdbe", value: "ACDBE" },
  { key: "cd/other", value: "Other" },
]; 
