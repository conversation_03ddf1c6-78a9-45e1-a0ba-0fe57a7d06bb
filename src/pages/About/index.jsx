import Banner from "../../components/Common/Banner";
import LatestNews from "../../components/Common/LatestNews";
import LetsDoBusiness from "../../components/Common/LetsDoBusiness";
import Highlights from "../../components/Common/Highlights";
import Brands from "../../components/Leasing/Brands";
import LeasingSection from "../../components/Leasing/LeasingSection";
import Section from "../../components/About/Section";
import SectionWithLogos from "../../components/About/SectionWithLogos";
const AboutUsPage = ({ aboutHeroImage, latestNews, letsDoBusiness, highlights, aboutPageCarousel, aboutPageSection }) => {

    let items = aboutPageCarousel.items?.filter(item => item?.carouselImage?.url != null).map(item => {
        return {
            url: item?.carouselImage?.url,
            description: item?.carouselImage?.description
        }
    })

    const brands = {
        items: [
            {
                imageCollection: {
                    items: items
                }

            }
        ]
    }

    return (
        <div className="about-page">
            <Banner heroImage={aboutHeroImage} />
            <LeasingSection section={aboutPageSection} />
            <Section section={aboutPageSection}/>
            <Highlights highlights={highlights} />
            <Brands brands={brands} carouselTitle="Award-Winning Portfolio" />
            <SectionWithLogos section={aboutPageSection}/>
            <LatestNews latestNewsandStories={latestNews} />
            <LetsDoBusiness letsDoBusiness={letsDoBusiness} />
        </div>
    )
}

export default AboutUsPage;