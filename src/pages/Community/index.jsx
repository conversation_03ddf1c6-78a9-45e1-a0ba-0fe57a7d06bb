import Banner from "../../components/Common/Banner";
import LatestNews from "../../components/Common/LatestNews";
import LetsDoBusiness from "../../components/Common/LetsDoBusiness";
import AwardsAndHonors from "../../components/Common/AwardsAndHonors";
import CardWithImage from "../../components/Common/CardWithImage";
import WhereWeFocus from "../../components/Community/WhereWeFocus";
import CardWithVideo from "../../components/Common/CardWithVideo";
const CommunityPage = ({ communityHeroImage, latestNews, letsDoBusiness, partners, cardwithImage, cardWithVideo, whereWeFocus }) => {
  return (
    <>
      <Banner heroImage={communityHeroImage} />
      <CardWithVideo cardWithVideo={cardWithVideo} />
      <CardWithImage cardWithImage={cardwithImage} />
      <WhereWeFocus whereWeFocus={whereWeFocus} />
      <AwardsAndHonors awardsAndHonors={partners} sectionClassName="partner-section" />
      <LatestNews latestNewsandStories={latestNews} />
      <LetsDoBusiness letsDoBusiness={letsDoBusiness} />
    </>
  );
};
export default CommunityPage;
