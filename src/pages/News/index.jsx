import Banner from "../../components/Common/Banner";
import AwardsAndHonors from "../../components/Common/AwardsAndHonors";
import LetsDoBusiness from "../../components/Common/LetsDoBusiness";
import LatestNewsAndStories from "../../components/News/LatestNewsAndStories";
import AllNews from "../../components/News/AllNews";
const NewsPage = ({ newsPageHeroImage,newsPageAwardsAndHonors,newsPageLetsDoBusiness,latestNewsandStories, news, currentPage }) => {
  return (
    <>
      <Banner heroImage={newsPageHeroImage} />
      <LatestNewsAndStories latestNewsandStories={latestNewsandStories} />
      <AllNews news={news} currentPage={currentPage}/>
      <AwardsAndHonors awardsAndHonors={newsPageAwardsAndHonors} />
      <LetsDoBusiness letsDoBusiness={newsPageLetsDoBusiness}/>
    </>
  );
};

export default NewsPage;
