import { documentToReactComponents } from "@contentful/rich-text-react-renderer";
import { BLOCKS } from "@contentful/rich-text-types";
import SupplierDiversityForm from "../../components/Common/SupplierDiversityForm";
import { contactUs } from "../../../api";
import { FORM_TYPES } from "../../../lambda/constants";
import toast from "../../components/Common/Toast";

export const SupplierDiversityPage = ({ supplierPageDetails }) => {
    const { items } = supplierPageDetails || {};
    const [{ banner: { url, title }, sectionTitle1, sectionTitle2, section } = {}] = items || [];

    const options = {
        renderNode: {
            [BLOCKS.UL_LIST]: (node, children) => <ul className="list">{children}</ul>
        },
    };

    const handleFormSubmit = async (values, { resetForm }) => {
        try {
            await contactUs(FORM_TYPES.REGISTER_NOW, values);
            resetForm();
            toast({ type: "success", message: "Thank you for your submission!" });
        } catch (error) {
            toast({ type: "error", message: "Something went wrong. Please try again." });
        }
    };

    const handleFormSuccess = () => {
        // Additional success handling for page context if needed
        // Could scroll to top, show additional message, etc.
    };

    const handleFormError = (error) => {
        // Additional error handling for page context if needed
        console.error("Form submission error:", error);
    };

    return (
        <div className="supplier-diversity-page">
            <section className="banner-section">
                <img src={url} alt="" />
                <div className="container">
                    <div className="banner-text">
                        <h1>{title?.toUpperCase()}</h1>
                    </div>
                </div>
            </section>
            <section className="supplier-diversity-section">
                <div className="container">
                    <div className="col-5 sub-container">
                        <h2>{sectionTitle1}</h2>
                        <h2 className="">{sectionTitle2}</h2>
                        <div className="sub-info">
                            {documentToReactComponents(section?.json, options)}
                        </div>
                    </div>
                    <div className="col-7 form-container">
                        <div className="title">
                            <h2>Register Now!</h2>
                        </div>
                        <SupplierDiversityForm
                            onSubmit={handleFormSubmit}
                            onSuccess={handleFormSuccess}
                            onError={handleFormError}
                            isModal={false}
                            submitButtonText="Submit"
                            submitButtonClass="btn-primary"
                            formClass="page-form"
                        />
                    </div>
                </div>
            </section>
        </div>
    );
};
