import LetsDoBusiness from "../../components/Common/LetsDoBusiness";
import LatestNews from "../../components/Common/LatestNews";
import NewsDetail from "../../components/News/NewsDetail";
const NewsDetailsPage = ({ newsDetail, letsDoBusiness }) => {
  const latestNewsandStories = newsDetail?.items[0]?.relatedNewsCollection;
  return (
    <>
      <NewsDetail newsDetail={newsDetail} />
      <LatestNews latestNewsandStories={latestNewsandStories} title="Related News" />
      <LetsDoBusiness letsDoBusiness={letsDoBusiness} />
    </>
  );
};

export default NewsDetailsPage;
