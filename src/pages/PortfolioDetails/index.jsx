import Banner from "../../components/Common/Banner";
import HightLights from "../../components/Common/Highlights";
import { documentToReactComponents } from "@contentful/rich-text-react-renderer";
import { INLINES } from "@contentful/rich-text-types";
import TabSection from "../../components/Portfolio/TabSection";
import LatestNews from "../../components/Common/LatestNews";
import LetsDoBusiness from "../../components/Common/LetsDoBusiness";
import { useRouter } from "next/router";
import JFKTabSection from "../../components/Portfolio/TabSection/JFK";
const PortfolioDetailsPage = ({ portfolioDetail, eventNews, terminalOnenews }) => {
  const router = useRouter();
  const { items: detail } = portfolioDetail;
  let heroImage = {};
  let portfolioHighLights = {};
  let letsDoBusiness = {};
  heroImage["items"] = [
    {
      title: detail && detail[0]?.title,
      subTitle: detail && detail[0]?.subTitle,
      location: detail && detail[0]?.location,
      bannerImage: {
        url: detail && detail[0]?.heroImage?.url,
      },
    },
  ];

  portfolioHighLights["items"] = [
    {
      highlightsCollection: {
        items: detail[0]?.portfolioHighlightsCollection?.items,
      },
    },
  ];

  letsDoBusiness["items"] = [
    {
      card: {
        title: detail && detail[0]?.letsDoBusiness?.title,
        description: detail && detail[0]?.letsDoBusiness?.description,
        link: detail && detail[0]?.letsDoBusiness?.link,
        linkText: detail && detail[0]?.letsDoBusiness?.linkText,
        image: {
          url: detail && detail[0]?.letsDoBusiness?.image?.url,
        },
      },
    },
  ];
  console.log(router.asPath)

  const validPathsMap = {
    "/portfolio/los-angeles-international-airport-lax/": TabSection,
    "/portfolio/johnf-kennedy-international-airport-t1/": JFKTabSection,
    "/newterminalone/": JFKTabSection
  };


  let ComponentToRender = validPathsMap[router.asPath];

  if (router.asPath.indexOf("portfolio/johnf-kennedy-international-airport-t1")) {
    ComponentToRender = JFKTabSection;
  }

  const showLetsDoBusiness = !validPathsMap[router.asPath] || router.asPath == "/portfolio/los-angeles-international-airport-lax/";


  const renderOptions = {
    renderNode: {
      [INLINES.HYPERLINK]: (node) => {
        return (
          <a
            href={node.data.uri}
            target={`${node.data.uri.startsWith("https://") ? "_blank" : ""}`}
          >
            {node.content[0].value}
          </a>
        );
      },
    },
  };

  return (
    <>
      <Banner heroImage={heroImage} />
      <section className="sub-section">
        <div className="container">
          <div className="title">
            <h2>{detail && detail[0]?.topSectionTitle}</h2>
            {detail && detail[0]?.topSectionSubTitle && (
              <h3>{detail && detail[0]?.topSectionSubTitle}</h3>
            )}
            {detail &&
              detail[0]?.topSectionDescription?.json &&
              documentToReactComponents(
                detail && detail[0]?.topSectionDescription?.json
              )}
          </div>
          {detail && detail[0]?.topSectionLogo?.url && (
            <div className="title-logo">
              <img src={detail[0]?.topSectionLogo?.url} alt="" />
            </div>
          )}
        </div>
      </section>
      <HightLights
        highlights={portfolioHighLights}
        highlightClassName="highlight-section"
        highlightNote={detail && detail[0]?.highlightNote}
      />
      <section className="download-section">
        <div className="container">
          <div className="download-text">
            {documentToReactComponents(
              detail && detail[0]?.portfolioDescription?.json,
              renderOptions
            )}
          </div>
          <div className="download-action">
            {detail && detail[0]?.factSheetText && (
              <>
                <a
                  href={(detail && detail[0]?.factSheet?.url) || "#"}
                  target="_blank"
                  className="download-doc"
                >
                  <span>{detail && detail[0]?.factSheetText}</span>
                  <span>
                    <strong>PDF</strong>
                    <i></i>
                  </span>
                </a>
                <a
                  href={(detail && detail[0]?.factSheet?.url) || "#"}
                  target="_blank"
                  className="btn"
                >
                  <span>DOWNLOAD</span>
                </a>
              </>
            )}
          </div>
        </div>
      </section>
      {detail &&
        detail[0]?.portfolioTabsCollection?.items?.length > 0 &&
        ComponentToRender &&
        (ComponentToRender === JFKTabSection ? (
          <ComponentToRender
            tabs={detail[0]?.portfolioTabsCollection}
            eventNews={eventNews}
            terminalOnenews={terminalOnenews}
          />
        ) : (
          <ComponentToRender tabs={detail[0]?.portfolioTabsCollection} />
        ))}

      {detail && detail[0]?.newsCollection?.items?.length > 0 && (
        <LatestNews
          latestNewsandStories={detail[0]?.newsCollection}
          title={detail[0]?.newsTitle}
        />
      )}
      {detail && detail[0]?.letsDoBusiness && (
        showLetsDoBusiness && <LetsDoBusiness letsDoBusiness={letsDoBusiness} />
      )}
    </>
  );
};

export default PortfolioDetailsPage;
