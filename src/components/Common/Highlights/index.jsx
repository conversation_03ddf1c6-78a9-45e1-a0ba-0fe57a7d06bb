const Highlights = ({ highlights, highlightClassName, highlightNote }) => {
  const { items } = highlights;
  return (
    <>
      <section className={highlightClassName || "highlights-section"}>
        <div className="container">
          {!highlightClassName && (
            <div className="title">
              <h2>By The Numbers</h2>
            </div>
          )}
          <div className="flex-space-around">
            {items &&
              items[0]?.highlightsCollection?.items?.map((item, index) => {
                return (
                  <div className="flex-box" key={index}>
                    <h2>{item?.stats}</h2>
                    {item?.title?.indexOf("(1)") > 0 ? (
                      <>
                        <p>
                          {item?.title.split("(1)")[0]}
                          <sup>(1)</sup>
                        </p>
                      </>
                    ) : (
                      <p>{item?.title}</p>
                    )}
                  </div>
                );
              })}
          </div>
          {highlightNote && (
            <div className="highlight-note">{highlightNote}</div>
          )}
        </div>
      </section>
    </>
  );
};

export default Highlights;
