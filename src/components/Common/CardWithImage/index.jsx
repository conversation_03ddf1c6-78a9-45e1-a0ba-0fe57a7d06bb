import { documentToReactComponents } from "@contentful/rich-text-react-renderer";
import { BLOCKS } from "@contentful/rich-text-types"
const CardWithImage = ({ cardWithImage }) => {
  let { items } = cardWithImage;
  items = items.filter(item => item?.card?.title != undefined);  
  const options = {
    renderNode: {
      [BLOCKS.UL_LIST]: (node, children) => <ul className="list">{children}</ul>
    },
  };
  return (
    <>
      {items && items.length > 0 && (
        <section className="mission-section">
          <div className="container">
            <div className="flex">
              <div className="mission-img">
                <img src={items[0]?.card?.image?.url} alt="" />
              </div>
              <div className="mission-info">
                <h2>{items[0]?.card?.title}</h2>
                {documentToReactComponents(items[0]?.card?.content?.json, options)}
              </div>
            </div>
          </div>
        </section>
      )}
    </>
  );
};
export default CardWithImage;
