import { documentToReactComponents } from "@contentful/rich-text-react-renderer";
import HighlightWords from "../HighlightWords";
import React from "react";
import { INLINES } from "@contentful/rich-text-types";

const CardWithVideo = ({ cardWithVideo, isSustainabilityPage = false }) => {
  let { items } = cardWithVideo;
  items = items.filter((item) => item?.card?.title != undefined);

  const handleClick = (e) => {
    const modalOverlay = document.querySelectorAll(["div.ReactModal__Overlay"])
    modalOverlay[0].classList.add("ReactModal__FadeIn");
    e.preventDefault();
  }

  const renderOptions = {
    renderNode: {
      [INLINES.HYPERLINK]: (node) => {
        return (
          <a href={node.data.uri} className="link" target={`${node.data.uri.startsWith("https://") ? "_blank" : ""}`}>
            {node.content[0].value}
          </a>
        );
      },
    },
  };

  return (
    <>
      {items && items.length > 0 && (
        <section className="community-section">
          <div className="container">
            <div className="sub-container">
              {isSustainabilityPage ? (<HighlightWords positions={[1, 2]} sentence={items[0]?.card?.title} />) : (<h2>{items[0]?.card?.title}</h2>)}
              {items[0]?.card?.videoLink && (
                <div className="video">
                  <iframe
                    width="600"
                    height="460"
                    src={items[0]?.card?.videoLink}
                    title="YouTube video player"
                    frameBorder="0"
                    allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
                    allowFullScreen
                  ></iframe>
                </div>)}
              <div className="sub-info">
                {documentToReactComponents(items[0]?.card?.content?.json, renderOptions)}
                <div className="sub-btn">
                  {items[0]?.card?.link ? (
                    <>
                      <a href={items[0]?.card?.link} className="btn">
                        {items[0]?.card?.linkText}
                      </a>
                    </>
                  ) : (
                    <>
                      {items[0]?.card?.linkText &&
                        <a href="#" className="btn" onClick={handleClick}>
                          {items[0]?.card?.linkText}
                        </a>}
                    </>
                  )}
                </div>
              </div>
            </div>
          </div>
        </section>
      )}
    </>
  );
};

export default CardWithVideo;
