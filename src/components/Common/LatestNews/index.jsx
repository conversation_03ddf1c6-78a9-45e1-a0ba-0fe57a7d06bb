import Slider from "react-slick";
import Link from "next/link";
const sliderSettings = {
  centerMode: true,
  variableWidth: true,
  slidesToShow: 1,
  swipe: true,
  responsive: [
    {
      breakpoint: 992,
      settings: {
        centerMode: false,
        variableWidth: false,
      },
    },
  ],
};
const LatestNews = ({ latestNewsandStories, title }) => {
  return (
    <>
      {latestNewsandStories?.items && latestNewsandStories?.items?.length > 0 && <section className="news-section">
        <div className="title">
          <h2>{title || "Latest News & Top Stories"}</h2>
        </div>
        <Slider {...sliderSettings} className="news-carousel">
          {latestNewsandStories?.items?.map((item, index) => {
            return (
              <div className="news-item" key={index}>
                <div className="news-img">
                  <Link href={item?.slug ? `/${item?.slug}/` : "#"}>
                    <img src={item?.bannerImage?.url} alt="" />
                  </Link>
                  {/* <img src={item?.bannerImage?.url} alt="" /> */}
                </div>
                <div className="news-info">
                  <h4>{item?.tags}</h4>
                  <h3>
                    {item?.title}
                  </h3>
                  <p>
                    {item?.subTitle}
                  </p>
                  <div className="news-btn">
                    <Link href={item?.slug ? `/${item?.slug}/` : "#"} className="btn">
                      Read more
                    </Link>
                  </div>
                </div>
              </div>
            );
          })}
        </Slider>
      </section>}
    </>
  );
};

export default LatestNews;
