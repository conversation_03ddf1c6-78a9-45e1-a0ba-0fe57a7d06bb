const LetsDoBusiness = ({ letsDoBusiness }) => {
  const { items } = letsDoBusiness;
  return (
    <>
      <section className="business-section">
        <div className="container">
          <div className="flex-reverse">
            <div className="db-img">
              <img src={items && items[0]?.card?.image?.url} alt="" />
            </div>
            <div className="db-info">
              <h2>{items && items[0]?.card?.title}</h2>
              <p>{items && items[0]?.card?.description}</p>
              <div className="db-btn">
                <a
                  href={
                    items && items[0]?.card?.link ? items[0]?.card?.link : "#"
                  }
                  className="btn"
                >
                  <span>{items && items[0]?.card?.linkText}</span> <i></i>
                </a>
              </div>
            </div>
          </div>
        </div>
      </section>
    </>
  );
};

export default LetsDoBusiness;
