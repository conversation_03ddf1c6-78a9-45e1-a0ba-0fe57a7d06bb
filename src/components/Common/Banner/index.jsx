const Banner = ({ heroImage }) => {
  const { items } = heroImage;
  return (
    <>
      <section className="banner-section">
        <img src={items && items[0]?.bannerImage?.url} alt="" />
        <div className="container">
          <div className="banner-text">
            {items && items[0]?.location && <span>{items && items[0]?.location}</span>}
            <h1>{items && items[0]?.title}</h1>
            {items && items[0]?.subTitle && <p>{items && items[0]?.subTitle}</p>}
          </div>
        </div>
      </section>
    </>
  );
};

export default Banner;
