import React from "react";
const HighlightWords = ({ sentence, positions = [-1] }) => {
    const words = sentence.split(' ');

    return (
        <h2>
            {words.map((word, index) => (
                <React.Fragment key={index}>
                    {positions.includes(index) ? (
                        <span>{word}</span>
                    ) : (
                        word
                    )}
                    {index < words.length - 1 && ' '}
                </React.Fragment>
            ))}
        </h2>
    );
};

export default HighlightWords;