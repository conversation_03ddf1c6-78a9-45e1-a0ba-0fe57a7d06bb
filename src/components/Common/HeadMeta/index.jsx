import Head from "next/head";

const HeadMeta = ({ meta, metaogImage }) => {
  const metaDetail = meta.items[0];
  return (
    <>
      <Head>
        <title>{metaDetail?.title || ""}</title>
        <meta name="description" content={metaDetail?.description || ""} />
        <link rel="icon" href="/assets/favicon.png" />
        <link rel="canonical" href={metaDetail?.canonicalUrl || ""}></link>
        {
          metaogImage && (
            <>
              <meta property='og:title' content={metaDetail?.title || ""} />
              <meta property='og:image' content={metaogImage} />
              <meta property='og:description' content={metaDetail?.description || ""} />
              <meta property='og:url' content={metaDetail?.canonicalUrl || ""} />
            </>
          )
        }
      </Head>
    </>
  );
};

export default HeadMeta;
