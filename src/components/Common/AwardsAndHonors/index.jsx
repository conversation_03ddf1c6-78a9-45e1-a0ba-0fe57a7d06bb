import Slider from "react-slick";

const sliderSettings = {
  dots: true,
  arrows: false,
  infinite: true,
  speed: 300,
  slidesToShow: 4,
  slidesToScroll: 4,
  variableHeight:true,
  centerMode:false,
  responsive: [
    {
      breakpoint: 1023,
      settings: {
        slidesToShow: 3,
        slidesToScroll: 3,
      },
    },
    {
      breakpoint: 768,
      settings: {
        slidesToShow: 1,
        slidesToScroll: 1,
      },
    },
  ],
};
const AwardsAndHonors = ({ awardsAndHonors, sectionClassName }) => {
  const { items } = awardsAndHonors;
  const defaultTitle = "AWARDS & HONORS";
  const defaultSectionClass = "awards-section";
  
  const sectionTitle = items && items?.length > 0 && items[0]?.title || defaultTitle; 
  const sectionClass = sectionClassName || defaultSectionClass;
  return (
    <>
      <section className={sectionClass}>
        <div className="container">
          <div className="title">
            <h2>{sectionTitle}</h2>
          </div>
          <Slider {...sliderSettings} className="awards-carousel">
            {items &&
              items[0]?.logosCollection?.items?.map((item, index) => {
                return (
                  <div className="awards-item" key={index}>
                    <div className="carousel-logo">
                      <img src={item?.logo?.url} alt="" />
                    </div>
                    {item?.title && <h4>{item?.title}</h4>}
                    {item?.subTitle && <p>{item?.subTitle}</p>}
                  </div>
                );
              })}

          </Slider>
        </div>
      </section>
    </>
  );
};

export default AwardsAndHonors;
