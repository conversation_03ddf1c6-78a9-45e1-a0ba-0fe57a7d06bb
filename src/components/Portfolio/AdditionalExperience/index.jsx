import Slider from "react-slick";

const sliderSettings = {
  dots: true,
  arrows: false,
  infinite: true,
  speed: 300,
  slidesToShow: 3,
  slidesToScroll: 3,
  responsive: [
    {
      breakpoint: 768,
      settings: {
        centerMode: true,
        slidesToShow: 1,
      },
    },
  ],
};

const AdditionalExperience = ({ additionalExperience }) => {
  const { items } = additionalExperience;
  
  return (
    <>
      <section className="awards-section">
        <div className="container">
          <div className="title">
            <h2>Additional Experience</h2>
          </div>
          <Slider {...sliderSettings} className="awards-portfolio-carousel">
            {items &&
              items[0]?.logosCollection?.items?.map((item, index) => {
                return (
                  <div className="awards-item" key={index}>
                    <div className="carousel-logo">
                      <img src={item?.logo?.url} alt="" />
                    </div>
                    <h4>{item?.title}</h4>
                  </div>
                );
              })}
          </Slider>
        </div>
      </section>
    </>
  );
};

export default AdditionalExperience;
