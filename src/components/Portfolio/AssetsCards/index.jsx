import Link from "next/link";
const AssetsCards = ({ cards }) => {
  const { items } = cards;
  return (
    <>
      <section className="portfolio-section">
        <div className="container">
          <div className="title">
            <h2>{items && items[0]?.title}</h2>
            <p>{items && items[0]?.subTitle}</p>
          </div>
          <div className="assets">
            {items &&
              items[0]?.cardsCollection?.items?.map((item, index) => {
                return (
                 item && <div className="card" key={index}>
                    <div className="card-img">
                      <Link href={`/portfolio/${item?.slug}/`}>
                        <img src={item?.heroImage?.url} alt="" />
                      </Link>
                    </div>
                    <div className="card-info">
                      <div className="card-logo">
                        <img src={item?.logo?.url} alt="" />
                      </div>
                      <div className="card-text">
                        <p>{item?.location}</p>
                        <h3>{item?.title}</h3>
                        <h4>{item?.subTitle}</h4>
                      </div>
                      <Link href={`/portfolio/${item?.slug}/`} className="btn">
                        <span>Learn More</span>
                        <i></i>
                      </Link>
                    </div>
                    <Link href={`/portfolio/${item?.slug}/`}></Link>
                  </div>
                );
              })}
          </div>
        </div>
      </section>
    </>
  );
};

export default AssetsCards;
