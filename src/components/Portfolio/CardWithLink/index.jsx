import { documentToReactComponents } from "@contentful/rich-text-react-renderer";
const CardWithLink = ({ cardWithLink }) => {
  const { items } = cardWithLink;
  return (
    <>
      <section className="global-portfolio-section">
        <div className="container">
          <div className="flex">
            <div className="db-img">
              <img src={items && items[0]?.card?.image?.url} alt="" />
            </div>
            <div className="db-info">
              <h2>{items && items[0]?.card?.title}</h2>
              {items && items[0]?.card?.content?.json && documentToReactComponents(items && items[0]?.card?.content?.json)}
              <div className="db-btn">
                <a href={items && items[0]?.card?.link ? items && items[0]?.card?.link : "#"} className="btn">
                  <span>{items && items[0]?.card?.linkText}</span> <i></i>
                </a>
              </div>
            </div>
          </div>
        </div>
      </section>
    </>
  );
};

export default CardWithLink;
