import { useState } from "react";
import { documentToReactComponents } from "@contentful/rich-text-react-renderer";
const TabSection = ({ tabs }) => {
  const [activeTab, setActiveTab] = useState(tabs?.items[0]?.tabTitle);
  const [activeImage, setActiveImage] = useState(tabs?.items[0]?.tabImage?.url);
  const [activeTitle, setActiveTitle] = useState(tabs?.items[0]?.title);
  const [activeDescription, setActiveDescription] = useState(tabs?.items[0]?.description);
  const [activeContent, setActiveContent] = useState(tabs?.items[0]?.content?.json);
  const [activeTabLogo, setActiveTabLogo] = useState(tabs?.items[0]?.logo?.url);
  const [activeLogoTitle, setActiveLogoTitle] = useState(tabs?.items[0]?.logoTitle);
  const [activeLogoSubTitle, setActiveLogoSubTitle] = useState(tabs?.items[0]?.logoSubTitle);
  const [activeHighlights, setActiveHighlights] = useState(tabs?.items[0]?.highlightsCollection);
  const [toggle, setToggle] = useState(false);
  const handleTabChange = (tab) => {
    setActiveTab(tab?.tabTitle);
    setActiveTitle(tab?.title);
    setActiveImage(tab?.tabImage?.url);
    setActiveDescription(tab?.description);
    setActiveContent(tab?.content?.json);
    setActiveTabLogo(tab?.logo?.url);
    setActiveLogoTitle(tab?.logoTitle);
    setActiveLogoSubTitle(tab?.logoSubTitle);
    setActiveHighlights(tab?.highlightsCollection);
    setToggle((toggle) => !toggle);
  };

  return (
    <section className="tabs-section">
      <div className="tabs-image">
        <div className="tab-content current">
          <img src={activeImage} alt="" style={{ transition: "width 0.5s, height 0.5s, opacity 0.5s 0.5s" }} />
        </div>
      </div>
      <div className="tabs-container">
        <nav className={`tabs ${toggle ? "expanded" : ""}`}>
          <ul className={toggle ? "expanded" : ""}>
            {tabs?.items?.map((item, i) => {
              return (
                <li key={i} className={`${activeTab == item?.tabTitle ? "active current" : ""}`} onClick={() => handleTabChange(item)}>

                  <a>{item?.tabTitle}</a>

                  <span></span>
                </li>

              );
            })}
          </ul>
        </nav>
      </div>
      <div className="tabs-content">
        <div className="container">
          <div className="tab-content current">
            <div className="tab-details">
              <div className="tab-info">
                <h2>{activeTitle}</h2>
                <h3>{activeDescription}</h3>
                {documentToReactComponents(activeContent)}
              </div>
              {activeTabLogo && (
                <div className="logo-info">
                  <div className="carousel-logo">
                    <img src={activeTabLogo} alt="" />
                  </div>
                  <h4>{activeLogoTitle}</h4>
                  <p>{activeLogoSubTitle}</p>
                </div>
              )}
            </div>
            <div className="highlight-section">
              <div className="container">
                <div className="flex-space-around">
                  {activeHighlights?.items?.map((item, i) => {
                    return (
                      <div className="flex-box" key={i}>
                        <h2>{item?.stats}</h2>
                        {item?.title?.indexOf("(1)") > 0 ? (
                          <>
                            <p>
                              {item?.title.split("(1)")[0]}
                              <sup>(1)</sup>
                            </p>
                          </>
                        ) : (
                          <p>{item?.title}</p>
                        )}
                      </div>
                    );
                  })}
                </div>
                <div className="highlight-note">(1) Figures represent CY 2022.</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default TabSection;
