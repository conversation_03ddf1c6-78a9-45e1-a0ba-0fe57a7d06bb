import Link from "next/link";
const Footer = ({ footerLinks }) => {
  return (
    <>
      <footer className="footer">
        <div className="container">
          <div className="footer-top">
            <div className="footer-left">
              <div className="footer-logo">
                <img src="/assets/images/logo-urw-white.svg" alt="" />
              </div>
              <div className="social-links">
                <a href="https://www.linkedin.com/showcase/unibail-rodamco-westfield-airports/" target="_blank" className="linkedIn">
                  <img src="/assets/images/icon-linkedin.svg" alt="" />
                </a>
                <a href="https://www.youtube.com/channel/UC9Sky2KLHwIRSnbitLIzn2Q" className="youTube" target="_blank">
                  <img src="/assets/images/icon-youtube.svg"  alt="" />
                </a>
              </div>
            </div>
            <div className="footer-portfolio">
              <h4>Portfolio</h4>
              <ul>
                {footerLinks &&
                  footerLinks?.items?.length > 0 &&
                  footerLinks?.items?.map((item, index) => {
                    return (
                      <li key={index}>
                        <Link href={`/portfolio/${item.slug}/`}>{item.footerText}</Link>
                      </li>
                    );
                  })}
                {/* <li>
                  <a href="#">Los Angeles International Airport (LAX)</a>
                </li>
                <li>
                  <a href="#">Chicago O’Hare International Airport (ORD)</a>
                </li>
                <li>
                  <a href="#">John F. Kennedy International Airport (T1)</a>
                </li>
                <li>
                  <a href="#">Fulton Center</a>
                </li>
                <li>
                  <a href="#">John F. Kennedy International Airport (JFK)</a>
                </li>
                <li>
                  <a href="#">World Trade Centre</a>
                </li> */}
              </ul>
            </div>
            <div className="footer-btn">
              <a href="https://www.urw.com/" target="_blank" className="btn-outline">
                URW.COM <i></i>
              </a>
            </div>
          </div>
          <div className="footer-bottom">
            <div className="page-links">
              <a href="https://www.urw.com/legal-notice" target="_blank">LEGAL NOTICE</a>
              <a href="https://www.urw.com/privacy-policy" target="_blank">PRIVACY POLICY</a>
              <a href="https://www.westfield.com/terms-and-conditions" target="_blank">TERMS &amp; CONDITIONS</a>
            </div>
            <div className="copyright">© {new Date().getFullYear()} URW Airports, LLC</div>
          </div>
        </div>
      </footer>
    </>
  );
};

export default Footer;
