import Slider from "react-slick";
const settings = {
  slidesToShow: 1,
  slidesToScroll: 1,
  mobileFirst: true,
  dots: true,
  arrows: false,
  responsive: [
    {
      breakpoint: 768,
    },
    {
      breakpoint: 10000, // a unrealistically big number to cover up greatest screen resolution
      settings: "unslick",
    },
  ],
};
const WhyURW = ({ homePageWhyURW }) => {
  return (
    <>
      <section className="why-urw-section" id="why-urw-section">
        <div className="container">
          <div className="title">
            <h2>WHY URW?</h2>
          </div>
          <div className="flex mobile-carousel">
            <Slider {...settings} className="flex mobile-carousel">
              {homePageWhyURW?.items?.map((item, index) => {
                return (
                  <div className="flex-box" key={index}>
                    <div className="carousel-logo">
                      <img src={item?.icon?.url} alt="" />
                    </div>
                    <h3>{item?.title}</h3>
                    <p>{item?.description}</p>
                  </div>
                );
              })}
            </Slider>
          </div>
        </div>
      </section>
    </>
  );
};
export default WhyURW;
