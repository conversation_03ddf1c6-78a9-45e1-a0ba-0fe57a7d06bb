import Slider from "react-slick";
import Link from "next/link";
const settings = {
  slidesToShow: 1,
  slidesToScroll: 1,
  mobileFirst: true,
  dots: true,
  arrows: false,
  responsive: [
    {
      breakpoint: 768,
    },
    {
      breakpoint: 10000, // a unrealistically big number to cover up greatest screen resolution
      settings: "unslick",
    },
  ],
};
const AssetsCards = ({ homePageCards }) => {
  const { items } = homePageCards;
  return (
    <>
      <section className="assets-section">
        <div className="container">
          <div className="title">
            <h1>{items && items[0]?.title}</h1>
            <p>{items && items[0]?.subTitle}</p>
          </div>
          <Slider {...settings} className="assets mobile-carousel">
            {items &&
              items[0]?.cardsCollection?.items?.map((item, index) => {
                return (
                  item && <div className="card" key={index}>
                    <div className="card-img">
                      <Link href={`/portfolio/${item?.slug}/`}>
                        <img src={item?.heroImage?.url} alt="" />
                      </Link>
                    </div>
                    <div className="card-info">
                      <div className="card-logo">
                        <img src={item?.logo?.url} alt="" />
                      </div>
                      <div className="card-text">
                        <p>{item?.location}</p>
                        <h3>{item?.title}</h3>
                        <h4>{item?.subTitle}</h4>
                      </div>
                      <Link href={`/portfolio/${item?.slug}/`} className="btn">
                        <span>Learn More</span>
                        <i></i>
                      </Link>
                    </div>
                    <Link href={`/portfolio/${item?.slug}/`}></Link>
                  </div>
                );
              })}
            <div className="card card-explore" key="discoverporfolio">
              <div className="card-img">
                <img src="assets/images/discoverportfolio.jpg" alt="" />
              </div>
              <div className="card-explore-info">
                <div className="card-text">
                  <h2>DISCOVER OUR GLOBAL PORTFOLIO</h2>
                  <Link href="https://www.urw.com/portfolio/" target="_blank"  className="btn">
                  <span>Explore</span>
                  <i></i>
                </Link>
                </div>
              </div>
            </div>
          </Slider>
        </div>
      </section>
    </>
  );
};
export default AssetsCards;
