import Slider from "react-slick";
import { useEffect } from "react";
import Link from "next/link";
const HomeCarousel = ({ homePageHeroImages }) => {
  useEffect(() => {
    const firstElement = document.querySelectorAll(["div.home-carousel .slick-slide img"])[1];
    firstElement.classList.add("slide-animation");
  }, []);

  const beforeChange = (current, next) => {
    const currentElement = document.querySelectorAll(["div.home-carousel .slick-slide img"])[current + 1];
    const nextElement = document.querySelectorAll(["div.home-carousel .slick-slide img"])[next + 1];

    currentElement.classList.add("preve-slide");
    nextElement.classList.add("slide-animation");
  };

  const afterChange = (index) => {
    const prevElement = document.querySelectorAll("img.preve-slide");
    prevElement[0].classList.remove("preve-slide");
    prevElement[0].classList.remove("slide-animation");
  };

  const sliderSettings = {
    pauseOnHover: true,
    dots: true,
    infinite: true,
    autoplay: true,
    autoplaySpeed: 5000,
    slidesToShow: 1,
    beforeChange,
    afterChange,
  };



  return (
    <>
      <section className="home-carousel-section">
        <Slider {...sliderSettings} className="home-carousel">
          {homePageHeroImages.items.map((item, index) => {
            return (
              <div className="slide-item" key={index}>
                <img src={item?.banner?.url} alt="" />
                <div className="container">
                  <div className="slide-content">
                    <h2>{item?.title}</h2>
                    <p>{item?.description}</p>
                    <p>
                      <Link href={item?.link ? item?.link : "#"} scroll={false} className="btn">
                        <span>{item?.linkText}</span>
                        <i></i>
                      </Link>
                    </p>
                  </div>
                </div>
              </div>
            );
          })}
        </Slider>
      </section>
    </>
  );
};

export default HomeCarousel;
