import { useContext } from "react";
import RegistrationContext from "../../../../context/Registration/RegistrationContext";
const ContactBanner = ({ contactBanner }) => {

  const { setIsModalOpen } = useContext(RegistrationContext);

  const handleClick = (e) => {
    const modalOverlay = document.querySelectorAll(["div.ReactModal__Overlay"])
    modalOverlay[0].classList.add("ReactModal__FadeIn");
    e.preventDefault();
    setIsModalOpen(true);
  }


  const { items } = contactBanner;
  return (
    <section className="small-banner-section">
      <div className="container">
        <div className="banner-wrapper">
          <img src={items && items[0] && items[0]?.bannerImage?.url} alt="" />
          <div className="banner-text w-480">
            <h2>{items && items[0] && items[0]?.title}</h2>
            <button className="btn" onClick={handleClick}>
              <span>REGISTER NOW</span>
            </button>
          </div>
        </div>
      </div>
    </section>
  );
};

export default ContactBanner;
