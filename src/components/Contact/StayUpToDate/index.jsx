import { useState } from "react";
import toast from "../../Common/Toast";
import { contactUs } from "../../../../api";
import { FORM_TYPES } from "../../../../lambda/constants";
const StayUpToDate = () => {
  const [email, setEmail] = useState("");
  const [buttonSubmited, setButtonSubmited] = useState(false);

  const handleBlur = (e) => {
    if (e.target.value === "") {
      e.target.classList.remove("has-value");
    } else {
      e.target.classList.add("has-value");
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!/^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,4}$/i.test(email)) {
      toast({
        type: "error",
        message: "Please enter a valid email",
      });
      return false;
    }
    try {
      setButtonSubmited(true);
      await contactUs(FORM_TYPES.STAY_UP_TO_DATE, { email: email });
      setEmail("");
      toast({ type: "success", message: "Thank you!" });
      setButtonSubmited(false);
    } catch (error) {
      setButtonSubmited(false);
      toast({ type: "error", message: "Some Error occured!" });
    }
  };

  return (
    <section className="newsletter-section">
      <div className="container">
        <div className="flex">
          <h3>STAY UP TO DATE</h3>
          <div className="form-group">
            <input type="text" className="form-control" name="Email" id="email" value={email} onChange={(e) => setEmail(e.target.value)} onBlur={handleBlur} />
            <label htmlFor="user">Email</label>
          </div>
          <div className="form-submit">
            <input type="submit" value="Subscribe" className="btn" onClick={handleSubmit} />
            {buttonSubmited && <span>
              <span className="loader" />
            </span>}
          </div>
        </div>
      </div>
    </section>
  );
};

export default StayUpToDate;
