import Modal from "react-modal";
import { contactUs } from "../../../../api";
import { FORM_TYPES } from "../../../../lambda/constants";
import { useContext, useEffect } from "react";
import RegistrationContext from "../../../../context/Registration/RegistrationContext";
import toast from "../../Common/Toast";
import SupplierDiversityForm from "../../Common/SupplierDiversityForm";
const RegisterNow = () => {
  const { setIsModalOpen } = useContext(RegistrationContext);

  // Function to check hash on mount and when it changes
  useEffect(() => {
    const checkHash = () => {
      const modalOverlay = document.querySelectorAll(["div.ReactModal__Overlay"]);

      if (window.location.hash === "#diversity") {
        setIsModalOpen(true);
        modalOverlay[0].classList.add("ReactModal__FadeIn");
      } else {
        setIsModalOpen(false);
      }
    };

    setTimeout(() => {
      checkHash();
    }, 2000);

    window.addEventListener("hashchange", checkHash); // Listen for changes

    return () => {
      window.removeEventListener("hashchange", checkHash);
    };
  }, [setIsModalOpen]);

  const handleSubmit = async (values, { resetForm }) => {
    try {
      await contactUs(FORM_TYPES.REGISTER_NOW, values);
      handleClose();
      resetForm();
      toast({ type: "success", message: "Thank you!" });
    } catch (error) {
      toast({ type: "error", message: "Something went wrong" });
    }
  };

  const handleClose = () => {
    const modalOverlay = document.querySelectorAll(["div.ReactModal__Overlay"]);
    modalOverlay[0].classList.remove("ReactModal__FadeIn");
    setIsModalOpen(false);
  };

  return (
    <Modal isOpen={true} ariaHideApp={false}>
      <div className="modal-content">
        <div className="col-4">
          <div className="register-img">
            <img src="/assets/images/register-img.jpg" alt="" />
          </div>
          <div className="register-info">
            <h2>Join Our Supplier Diversity Network</h2>
            <p>
              Are you a minority-, female-owned, disadvantaged, or certified Airport Concessions Disadvantaged Business Enterprise (ACDBE) business interested in new business opportunities at
              airports across the country?
            </p>
            <ul className="list">
              <li>Connect with networking opportunities</li>
              <li>Exclusive invitations to participate in new business opportunities in leasing, contracting, and more</li>
              <li>Unlock access to mentorship and growth opportunities</li>
            </ul>
          </div>
        </div>
        <div className="col-8">
          <div className="modal-header">
            <h2>Register now!</h2>
            <button type="button" className="modal-close modal-toggle" onClick={handleClose}>
              <i className="icon-close"></i>
            </button>
          </div>
          <SupplierDiversityForm
            onSubmit={handleSubmit}
            isModal={true}
            submitButtonText="Submit"
            submitButtonClass="btn-primary"
          />
        </div>
      </div>
    </Modal>
  );
};

export default RegisterNow;
