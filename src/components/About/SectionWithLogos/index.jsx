import { documentToReactComponents } from "@contentful/rich-text-react-renderer";
import Link from "next/link";
import React from "react";
const SectionWithLogos = ({ section }) => {
    const { items } = section;

    return (
        <section className="green-section">
            <div className="title">
                {items && items.length > 0 && <h2>{items[0]?.section3Title}</h2>}
                <img src={items[0]?.section3Logo?.url} />
            </div>
            <div className="container">
                {documentToReactComponents(items[0]?.section3Description?.json)}
                <div className="flex">
                {items[0]?.section3ImagesCollection?.items?.map((item, index) => {
                    return (
                        <React.Fragment key={index}>
                            <div className="col-item">
                                <img src={item?.logo?.url} />
                                {documentToReactComponents(item?.description?.json)}
                            </div>
                        </React.Fragment>
                    )
                })}
                </div>
                <Link href={items[0]?.section3Link ? items[0]?.section3Link : "#"} target="_blank" className="btn">
                    <span>{items[0]?.section3LinkText}</span>
                    <i></i>
                </Link>
            </div>
        </section>
    )
}

export default SectionWithLogos;