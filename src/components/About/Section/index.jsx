import { documentToReactComponents } from "@contentful/rich-text-react-renderer";
const Section = ({ section }) => {
    const { items } = section;

    return (
        <section className="info-section">
            <div className="container">
                <div className="title">
                    {items && items.length > 0 && <h2>{items[0]?.section2Title}</h2>}
                    {items && items.length > 0 && <h3>{items[0]?.section2SubTitle}</h3>}
                </div>
                <div className="about-text">
                    {documentToReactComponents(items[0]?.section2Description?.json)}
                </div>
            </div>
        </section>
    )
}

export default Section;