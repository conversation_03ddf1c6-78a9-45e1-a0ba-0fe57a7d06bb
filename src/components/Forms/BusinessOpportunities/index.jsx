import { Formik, Form, ErrorMessage, Field } from "formik";
import * as Yup from "yup";
import TextError from "../../Common/TextError";
import toast from "../../Common/Toast";
import { contactUs } from "../../../../api";
import {
  businessIntrestsOptions,
  certificationOptions,
} from "../../../constants";
import React, { useState } from "react";
import { FORM_TYPES } from "../../../../lambda/constants";
/**
 * NOTE: In case of any addition/deletion in this option list,
 * Please update the file configuration in lambda/constant/index.js
 * Also, update the logic in lambda/csv-exporter/fileExport/index.js
 */

const BusinessOpportunitiesForm = () => {
  const [submited, setSubmited] = useState(false);
  const [buttonSubmited, setButtonSubmited] = useState(false);

  const logFormValues = (values) => {
    console.log(values);
  };
  const onChange = (value) => {
    setCaptchaValue(value);
  };

  const initialValues = {
    firstName: "",
    lastName: "",
    email: "",
    verifyEmail: "",
    mobile: "",
    companyName: "",
    businessAddress1: "",
    businessAddress2: "",
    city: "",
    state: "NY",
    zipCode: "",
    website: "",
    yearsInBusiness: "",
    businessInterests: [],
    certification: [],
    aboutBusiness: "",
    airportExperience: "",
    emailUpdates: true,
    textUpdates: false,
    advancedNetworkNews: true,
    termsAndConditions: true,
  };

  const validationSchema = Yup.object({
    firstName: Yup.string().required("First name is required"),
    lastName: Yup.string().required("Last name is required"),
    email: Yup.string().email("Invalid email").required("Email is required"),
    verifyEmail: Yup.string()
      .email("Invalid email")
      .oneOf([Yup.ref("email")], "Emails must match")
      .required("Verify email is required"),
    mobile: Yup.string()
      .matches(/^\d{10}$/, "Invalid mobile number")
      .required("Mobile number is required"),
    companyName: Yup.string().required("Company name is required"),
    businessAddress1: Yup.string().required("Business address is required"),
    city: Yup.string().required("City is required"),
    state: Yup.string().required("State is required"),
    zipCode: Yup.string().required("Zip code is required").matches(/^[0-9]+$/, "Must be only digits")
      .min(5, 'Zip Code must have atleast 5 digits'),
    yearsInBusiness: Yup.string().required("Years in business is required"),
    businessInterests: Yup.array(),
    // .min(1, "Select at least one business interest")
    // .required("Business interests are required"),
    aboutBusiness: Yup.string().required("Description is required"),
    airportExperience: Yup.string().required("Airport experience is required"),
    termsAndConditions: Yup.boolean().oneOf(
      [true],
      "You must agree to terms and conditions"
    ),
  });

  const handleSubmit = async (values, { resetForm }) => {
    try {
      // if (captchaValue == null) {
      //   toast({ type: "error", message: "Please check 'I am not robot'" });
      //   return false;
      // } else {
      setButtonSubmited(true);
      businessIntrestsOptions?.map((item) => {
        values[item.key] = values?.businessInterests.includes(item.value)
          ? true
          : false;
      });

      certificationOptions?.map((item) => {
        values[item.key] = values.certification.includes(item.value)
          ? true
          : false;
      });

      delete values.certification;
      delete values.businessInterests;

      await contactUs(FORM_TYPES.BUSINESS_OPPORTUNITY, values);
      toast({
        type: "success",
        message:
          "Thank you for registering your business with our Supplier Diversity Network.",
      });

      resetForm();
      setSubmited(true);
      setButtonSubmited(false);
    } catch (error) {
      console.log(error);
      setButtonSubmited(false);
      toast({ type: "error", message: "Something went wrong" });
    }
  };

  return (
    <>
      {!submited && (
        <Formik
          enableReinitialize={true}
          initialValues={initialValues}
          validationSchema={validationSchema}
          onSubmit={handleSubmit}
        >
          {({ handleChange, values, isSubmitting }) => {
            return (
              <Form className="business-form-box">
                <section className="business-form">
                  <div className="container">
                    <div className="title">
                      <h2>Join Our Supplier Network</h2>
                      <p>Register your business today to learn about upcoming opportunities and benefit from networking, mentorship and invitations to partner </p>
                      <span className="span-subtitle-2">(Required*)</span>
                    </div>
                    <div className="row">
                      <div className="col col-6">
                        <div className="form-field">
                          <h4>Contact Information</h4>

                          <div className="form-group">
                            <Field
                              id="firstName"
                              type="text"
                              value={values.firstName}
                              onChange={handleChange}
                              className={`business-form-control ${values.firstName ? "has-value" : ""
                                }`}
                            />
                            <label>First Name *</label>
                          </div>
                          <ErrorMessage
                            name="firstName"
                            component={TextError}
                          />
                          <div className="form-group">
                            <Field
                              id="lastName"
                              type="text"
                              value={values.lastName}
                              onChange={handleChange}
                              className={`business-form-control ${values.lastName ? "has-value" : ""
                                }`}
                            />
                            <label>Last Name *</label>
                          </div>
                          <ErrorMessage name="lastName" component={TextError} />

                          <div className="form-group">
                            <Field
                              id="email"
                              type="text"
                              value={values.email}
                              onChange={handleChange}
                              className={`business-form-control ${values.email ? "has-value" : ""
                                }`}
                            />
                            <label>Email *</label>
                          </div>

                          <ErrorMessage name="email" component={TextError} />
                          <div className="form-group">
                            <Field
                              id="verifyEmail"
                              type="text"
                              value={values.verifyEmail}
                              onChange={handleChange}
                              className={`business-form-control ${values.verifyEmail ? "has-value" : ""
                                }`}
                            />
                            <label>Verify Email *</label>
                          </div>
                          <ErrorMessage
                            name="verifyEmail"
                            component={TextError}
                          />
                          <div className="form-group">
                            <Field
                              id="mobile"
                              type="text"
                              value={values.mobile}
                              onChange={handleChange}
                              className={`business-form-control ${values.mobile ? "has-value" : ""
                                }`}
                            />
                            <label>Mobile Number *</label>
                          </div>
                          <ErrorMessage name="mobile" component={TextError} />
                        </div>
                        <div className="form-field">
                          <h4>Company Information</h4>
                          <div className="form-group">
                            <Field
                              id="companyName"
                              type="text"
                              value={values.companyName}
                              onChange={handleChange}
                              className={`business-form-control ${values.companyName ? "has-value" : ""
                                }`}
                            />
                            <label>Company Name *</label>
                          </div>
                          <ErrorMessage
                            name="companyName"
                            component={TextError}
                          />
                          <div className="form-group">
                            <Field
                              id="businessAddress1"
                              type="text"
                              value={values.businessAddress1}
                              onChange={handleChange}
                              className={`business-form-control ${values.businessAddress1 ? "has-value" : ""
                                }`}
                            />
                            <label>Business Address *</label>
                          </div>
                          <ErrorMessage
                            name="businessAddress1"
                            component={TextError}
                          />
                          <div className="form-group">
                            <Field
                              id="businessAddress2"
                              type="text"
                              value={values.businessAddress2}
                              onChange={handleChange}
                              className={`business-form-control ${values.businessAddress2 ? "has-value" : ""
                                }`}
                            />
                            <label>Business Address 2</label>
                          </div>

                          <div className="form-group">
                            <Field
                              id="city"
                              type="text"
                              value={values.city}
                              onChange={handleChange}
                              className={`business-form-control ${values.city ? "has-value" : ""
                                }`}
                            />
                            <label>City *</label>
                          </div>
                          <ErrorMessage name="city" component={TextError} />

                          <div className="two-colum">
                            <div className="form-group">
                              <select
                                className="business-form-control business-select"
                                defaultValue={"NY"}
                                values={values?.state}
                                onChange={handleChange}
                              >
                                <option value="AL">Alabama</option>
                                <option value="AK">Alaska</option>
                                <option value="AZ">Arizona</option>
                                <option value="AR">Arkansas</option>
                                <option value="CA">California</option>
                                <option value="CO">Colorado</option>
                                <option value="CT">Connecticut</option>
                                <option value="DE">Delaware</option>
                                <option value="DC">District Of Columbia</option>
                                <option value="FL">Florida</option>
                                <option value="GA">Georgia</option>
                                <option value="HI">Hawaii</option>
                                <option value="ID">Idaho</option>
                                <option value="IL">Illinois</option>
                                <option value="IN">Indiana</option>
                                <option value="IA">Iowa</option>
                                <option value="KS">Kansas</option>
                                <option value="KY">Kentucky</option>
                                <option value="LA">Louisiana</option>
                                <option value="ME">Maine</option>
                                <option value="MD">Maryland</option>
                                <option value="MA">Massachusetts</option>
                                <option value="MI">Michigan</option>
                                <option value="MN">Minnesota</option>
                                <option value="MS">Mississippi</option>
                                <option value="MO">Missouri</option>
                                <option value="MT">Montana</option>
                                <option value="NE">Nebraska</option>
                                <option value="NV">Nevada</option>
                                <option value="NH">New Hampshire</option>
                                <option value="NJ">New Jersey</option>
                                <option value="NM">New Mexico</option>
                                <option value="NY">New York</option>
                                <option value="NC">North Carolina</option>
                                <option value="ND">North Dakota</option>
                                <option value="OH">Ohio</option>
                                <option value="OK">Oklahoma</option>
                                <option value="OR">Oregon</option>
                                <option value="PA">Pennsylvania</option>
                                <option value="RI">Rhode Island</option>
                                <option value="SC">South Carolina</option>
                                <option value="SD">South Dakota</option>
                                <option value="TN">Tennessee</option>
                                <option value="TX">Texas</option>
                                <option value="UT">Utah</option>
                                <option value="VT">Vermont</option>
                                <option value="VA">Virginia</option>
                                <option value="WA">Washington</option>
                                <option value="WV">West Virginia</option>
                                <option value="WI">Wisconsin</option>
                                <option value="WY">Wyoming</option>
                              </select>
                            </div>

                            <div className="form-group">
                              <Field
                                id="zipCode"
                                type="number"
                                value={values.zipCode}
                                onChange={handleChange}
                                className={`business-form-control ${values.zipCode ? "has-value" : ""
                                  }`}
                              />
                              <label>Zip Code *</label>
                            </div>
                            <div> </div>
                            <ErrorMessage
                              name="zipCode"
                              component={TextError}
                            />
                          </div>
                          <div className="form-group">
                            <Field
                              id="website"
                              type="text"
                              value={values.website}
                              onChange={handleChange}
                              className={`business-form-control ${values.website ? "has-value" : ""
                                }`}
                            />
                            <label>Website</label>
                          </div>
                        </div>
                      </div>

                      <div className="col col-6 col-6-border">
                        <div className="form-field">
                          <h4>Business Interests</h4>
                          <h5>
                            I am interested in business opportunities in (select
                            all that apply): *
                          </h5>
                          <div className="checkbox-list">
                            <Field name="businessInterests">
                              {({ field }) => {
                                return businessIntrestsOptions?.map(
                                  (option) => {
                                    return (
                                      <React.Fragment key={option.key}>
                                        <div className="checkbox">
                                          <input
                                            type="checkbox"
                                            className="form-control"
                                            id={option.key}
                                            {...field}
                                            value={option.value}
                                            checked={
                                              field?.value?.includes(
                                                option?.value
                                              ) || false
                                            }
                                          ></input>
                                          <label htmlFor={option.key}>
                                            {option.value}
                                          </label>
                                        </div>
                                      </React.Fragment>
                                    );
                                  }
                                );
                              }}
                            </Field>
                          </div>
                          {/* <ErrorMessage
                            name="businessInterests"
                            component={TextError}
                          /> */}
                        </div>
                        <div className="form-field">
                          <h4>Certifications</h4>
                          <h5>
                            Is your business currently certified in any of the
                            following designations (select all that apply):
                          </h5>
                          <Field name="certification">
                            {({ field }) => {
                              return certificationOptions?.map((option) => {
                                return (
                                  <React.Fragment key={option.key}>
                                    <div className="checkbox">
                                      <input
                                        type="checkbox"
                                        className="form-control"
                                        id={option.key}
                                        {...field}
                                        value={option.value}
                                        checked={
                                          field?.value?.includes(
                                            option?.value
                                          ) || false
                                        }
                                      ></input>
                                      <label htmlFor={option.key}>
                                        {option.value}
                                      </label>
                                    </div>
                                  </React.Fragment>
                                );
                              });
                            }}
                          </Field>
                        </div>
                        <div className="form-field">
                          <h4>About</h4>
                          <div className="form-group">
                            <Field
                              id="aboutBusiness"
                              as="textarea"
                              value={values.aboutBusiness}
                              onChange={handleChange}
                              className={`form-control ${values.aboutBusiness ? "has-value" : ""
                                }`}
                              rows="3"
                            />
                            <label>
                              Briefly Describe your Business *
                            </label>
                          </div>
                          <ErrorMessage
                            name="aboutBusiness"
                            component={TextError}
                          />
                          <div className="form-group pt-30">
                            <Field
                              id="airportExperience"
                              as="textarea"
                              value={values.airportExperience}
                              onChange={handleChange}
                              className={`form-control ${values.airportExperience ? "has-value" : ""
                                }`}
                              rows="4"
                            />
                            <label>
                              Describe your airport business experience, or, if
                              you are new to airports, please explain why you
                              are interested in doing business here.*
                            </label>
                          </div>
                          <ErrorMessage
                            name="airportExperience"
                            component={TextError}
                          />
                          <div className="form-group">
                            <Field
                              id="yearsInBusiness"
                              type="text"
                              value={values.yearsInBusiness}
                              onChange={handleChange}
                              className={`form-control ${values.yearsInBusiness ? "has-value" : ""
                                }`}
                            />
                            <label>Years In Business *</label>
                          </div>
                          <ErrorMessage
                            name="yearsInBusiness"
                            component={TextError}
                          />
                        </div>
                        <h4>JFK Updates</h4>
                        <div className="checkbox">
                          <Field
                            type="checkbox"
                            id="emailUpdates"
                            name="emailUpdates"
                          />
                          <label htmlFor="emailUpdates">Email Updates</label>
                        </div>
                        <div className="checkbox">
                          <Field
                            type="checkbox"
                            id="advancedNetworkNews"
                            name="advancedNetworkNews"
                          />
                          <label htmlFor="advancedNetworkNews">
                            I'd also like to receive occasional email updates
                            from URW Airports, including information about
                            Advance Network news and opportunities.
                          </label>
                        </div>
                        <div className="checkbox">
                          <Field
                            type="checkbox"
                            id="termsAndConditions"
                            name="termsAndConditions"
                          />

                          <label htmlFor="termsAndConditions">
                            I've read to and agree to URW Airports' &nbsp;
                            <a
                              href="https://www.westfield.com/terms-and-conditions"
                              target="_blank"
                              rel="noreferrer"
                              className="link"
                            >
                              Terms Of Use
                            </a>{" "}
                            and &nbsp;
                            <a
                              href="https://www.urw.com/Privacy-Policy"
                              target="_blank"
                              rel="noreferrer"
                              className="link"
                            >
                              Privacy Policy
                            </a>
                          </label>
                        </div>
                        <br />
                        <ErrorMessage
                          name="termsAndConditions"
                          component={TextError}
                        />
                        {/* <ReCAPTCHA
                        sitekey="6Ld_dAInAAAAAPsDMNQaDgnrSBmPYnrb4KVkmVIy"
                        onChange={onChange}
                        style={{ display: "inline-block" }}
                        ref={captchaRef}
                      /> */}
                        <div className="business-form-submit">
                          <input
                            type="submit"
                            className="btn-primary"
                            value={"submit"}
                            disabled={isSubmitting}
                            onClick={() => {
                              logFormValues(values);
                            }}
                          />
                          {buttonSubmited && (
                            <span>
                              <span className="loader"></span>
                            </span>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                </section>
              </Form>
            );
          }}
        </Formik>
      )}
      {submited && (
        <div className="thanks-msg">
          <div className="check-background">
            <svg
              viewBox="0 0 65 51"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M7 25L27.3077 44L58.5 7"
                stroke="white"
                strokeWidth="13"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
          </div>
          <h2>Thank You</h2>
          <p>You're On Our List!</p>
        </div>
      )}
    </>
  );
};

export default BusinessOpportunitiesForm;
