import { useState } from "react";
import { Formik, Form, ErrorMessage, Field } from "formik";
import { contactUs } from "../../../../api";
import { FORM_TYPES } from "../../../../lambda/constants";
import * as Yup from "yup";
import TextError from "../../Common/TextError";
import toast from "../../Common/Toast";

const JFKContactUS = () => {
  const [submited, setSubmited] = useState(false);
  const [buttonSubmited, setButtonSubmited] = useState(false);

  const initialValues = {
    firstName: "",
    lastName: "",
    mobile: "",
    email: "",
    company: "",
    message: "",
    emailUpdates: true,
    inquiryType: "",
  };

  const validationSchema = Yup.object({
    firstName: Yup.string().required("First Name is required"),
    lastName: Yup.string().required("Last Name is required"),
    mobile: Yup.string()
      .matches(
        /^\d{10,14}$/,
        "Please enter a number with minimum 10 and maximum 14 digits"
      )
      .required("Mobile Number is required"),
    email: Yup.string()
      .email("Please enter a valid email")
      .required("Email is required"),
    company: Yup.string().required("Company name is required"),
    message: Yup.string().required("Message is required"),
    inquiryType: Yup.string().required("Inquiry Type is required"),
  });

  const handleBlur = (e) => {
    if (e.target.value == "") {
      e.target.classList.remove("has-value");
    } else {
      e.target.classList.add("has-value");
    }
  };

  const handleSubmit = async (values, { resetForm }) => {
    console.log(values);
    try {
      setButtonSubmited(true);
      await contactUs(FORM_TYPES.JFK_CONTACT_US, values);
      setSubmited(true);
      resetForm();
      setButtonSubmited(false);
    } catch (error) {
      setButtonSubmited(false);
      toast({ type: "error", message: "Something went wrong" });
    }
  };

  return (
    <>
      <section className="form-section">
        <div className="container">
          <div className="form-box">
            <div className="title">
              <h2>GET IN TOUCH</h2>
              <p>
                Have a general question or comment? Fill out the form below.
              </p>
              <span className="span-subtitle-2">(Required*)</span>
            </div>
            {!submited && (
              <Formik
                onSubmit={handleSubmit}
                enableReinitialize={true}
                initialValues={initialValues}
                validationSchema={validationSchema}
              >
                {({ handleChange, values, isSubmitting }) => {
                  return (
                    <Form>
                      <div className="form">
                        <div className="row">
                          <div className="jfk-col-6 ">
                            <div className="form-group">
                              <Field
                                id="firstName"
                                type="text"
                                value={values.firstName}
                                onChange={handleChange}
                                className="form-control"
                                onBlur={handleBlur}
                              />
                              <label htmlFor="firstName">First Name *</label>
                            </div>
                            <ErrorMessage
                              name="firstName"
                              component={TextError}
                            />
                          </div>
                          <div className="jfk-col-6 ">
                            <div className="form-group">
                              <Field
                                id="lastName"
                                type="text"
                                value={values.lastName}
                                onChange={handleChange}
                                className="form-control"
                                onBlur={handleBlur}
                              />
                              <label htmlFor="lastName">Last Name *</label>
                            </div>
                            <ErrorMessage
                              name="lastName"
                              component={TextError}
                            />
                          </div>
                        </div>

                        <div className="row">
                          <div className="jfk-col-6 ">
                            <div className="form-group">
                              <Field
                                id="email"
                                type="text"
                                value={values.email}
                                onChange={handleChange}
                                className="form-control"
                                onBlur={handleBlur}
                              />
                              <label htmlFor="email">Email *</label>
                              {/* <div className="tooltip" data-tip="Email Address" tabIndex="1">
                                <img src="/assets/images/information.svg" alt="" />
                              </div> */}
                            </div>
                            <ErrorMessage name="email" component={TextError} />
                          </div>
                          <div className="jfk-col-6 ">
                            <div className="form-group">
                              <Field
                                id="mobile"
                                type="text"
                                value={values.mobile}
                                onChange={handleChange}
                                className="form-control"
                                onBlur={handleBlur}
                              />
                              <label htmlFor="mobile">Mobile Number *</label>
                              {/* <div className="tooltip" data-tip="Mobile Number" tabIndex="2">
                                <img src="/assets/images/information.svg" alt="" />
                              </div> */}
                            </div>
                            <ErrorMessage name="mobile" component={TextError} />
                          </div>
                        </div>

                        <div className="row">
                          <div className="jfk-col-6 ">
                            <div className="form-group">
                              <Field
                                id="company"
                                type="text"
                                value={values.company}
                                onChange={handleChange}
                                className="form-control"
                                onBlur={handleBlur}
                              />
                              <label htmlFor="company">
                                Organization/Company *
                              </label>
                            </div>
                            <ErrorMessage
                              name="company"
                              component={TextError}
                            />
                          </div>
                        </div>

                        <div className="row">
                          <div className="jfk-col-6 ">
                            <div className="form-group">
                              <Field
                                id="message"
                                as="textarea"
                                value={values.otherInformation}
                                onChange={handleChange}
                                className="form-control"
                                onBlur={handleBlur}
                              />
                              <label htmlFor="message">Your message *</label>
                            </div>
                            <ErrorMessage
                              name="message"
                              component={TextError}
                            />
                          </div>
                        </div>
                        <div className="row">
                          <div className="jfk-col-6 ">
                            <div className="radio-group">
                              <label>Inquiry Type : *</label>
                              <Field name="inquiryType">
                                {({ field }) => (
                                  <>
                                    <div className="checkbox">
                                      <input
                                        type="radio"
                                        id="generalInquiry"
                                        {...field}
                                        value="General Inquiry"
                                        checked={
                                          field.value === "General Inquiry"
                                        }
                                        name="inquiryType"
                                        onChange={handleChange}
                                      />
                                      <label htmlFor="generalInquiry">
                                        General Inquiry
                                      </label>
                                    </div>
                                    <div className="checkbox">
                                      <input
                                        type="radio"
                                        id="mediaInquiry"
                                        {...field}
                                        value="Media Inquiry"
                                        checked={
                                          field.value === "Media Inquiry"
                                        }
                                        name="inquiryType"
                                        onChange={handleChange}
                                      />
                                      <label htmlFor="mediaInquiry">
                                        Media Inquiry
                                      </label>
                                    </div>
                                  </>
                                )}
                              </Field>
                            </div>
                            <ErrorMessage
                              name="inquiryType"
                              component={TextError}
                            />
                          </div>
                        </div>

                        <div className="row">
                          <div className="jfk-col-6 ">
                            <div className="checkbox">
                              <Field
                                type="checkbox"
                                id="emailUpdates"
                                name="emailUpdates"
                              ></Field>
                              <label htmlFor="emailUpdates">
                                I'd like to receive occasional email updates
                                from URW Airports, including information about
                                Advance Network news and opportunities.
                              </label>
                            </div>
                          </div>
                        </div>
                        <div className="row">
                          <div className="jfk-col-6 ">
                            <div className="form-group-footer">
                              <p>
                                By submitting I agree to all{" "}
                                <a
                                  href="https://www.westfield.com/terms-and-conditions"
                                  target="_blank"
                                  className="link"
                                >
                                  <strong>Terms of Use</strong>
                                </a>{" "}
                                and{" "}
                                <a
                                  href="https://www.urw.com/privacy-policy"
                                  target="_blank"
                                  className="link"
                                >
                                  <strong>Privacy Policy</strong>
                                </a>
                              </p>
                              <div className="form-submit">
                                <input type="submit" className="btn-primary" />
                                {buttonSubmited && (
                                  <span>
                                    <span className="loader"></span>
                                  </span>
                                )}
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </Form>
                  );
                }}
              </Formik>
            )}

            {submited && (
              <div className="thanks-msg">
                <div className="check-background">
                  <svg
                    viewBox="0 0 65 51"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M7 25L27.3077 44L58.5 7"
                      stroke="white"
                      strokeWidth="13"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    />
                  </svg>
                </div>
                <h2>Thank You</h2>
                <p>You're On Our List!</p>
              </div>
            )}
          </div>
        </div>
      </section>
    </>
  );
};

export default JFKContactUS;
