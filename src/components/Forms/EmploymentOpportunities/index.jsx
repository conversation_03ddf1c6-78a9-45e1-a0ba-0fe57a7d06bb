import { Formik, Form, ErrorMessage, Field } from "formik";
import * as Yup from "yup";
import TextError from "../../Common/TextError";
import toast from "../../Common/Toast";
import { contactUs } from "../../../../api";
import { employmentInterestsOptions } from "../../../constants";
import React, { useState } from "react";
import { useRef } from "react";
import { FORM_TYPES } from "../../../../lambda/constants";

/**
 * NOTE: In case of any addition/deletion in this option list,
 * Please update the file configuration in lambda/constant/index.js
 * Also, update the logic in lambda/csv-exporter/fileExport/index.js
 */

const EmploymentOpportunitiesForm = () => {
  // const captchaRef = useRef(null);
  // const [captchaValue, setCaptchaValue] = useState(null);
  const [submited, setSubmited] = useState(false);
  const [buttonSubmited, setButtonSubmited] = useState(false);

  // const onChange = (value) => {
  //   setCaptchaValue(value);
  // };

  const initialValues = {
    firstName: "",
    lastName: "",
    email: "",
    verifyEmail: "",
    mobile: "",
    homeAddress1: "",
    homeAddress2: "",
    city: "",
    state: "NY",
    zipCode: "",
    airportEmploymentExperience: "",
    employmentInterests: [],
    emailUpdates: true,
    textUpdates: false,
    advancedNetworkNews: true,
    termsAndConditions: true,
  };

  const validationSchema = Yup.object({
    firstName: Yup.string().required("First name is required"),
    lastName: Yup.string().required("Last name is required"),
    email: Yup.string()
      .required("Email is required")
      .email("Invalid email address"),
    verifyEmail: Yup.string()
      .email("Invalid email address")
      .oneOf([Yup.ref("email"), null], "Emails must match")
      .required("Verify email is required"),
    mobile: Yup.string()
      .required("Mobile number is required")
      .matches(/^\d{10}$/, "Please enter a valid 10-digit number"),
    homeAddress1: Yup.string().required("Home address is required"),
    city: Yup.string().required("City is required"),
    state: Yup.string().required("State is required"),
    zipCode: Yup.string().required("Zip code is required").min(5, 'Zip Code must have atleast 5 digits'),
    employmentInterests: Yup.array()
      .min(1, "Select at least one employment interest")
      .required("Employment interests are required"),
    termsAndConditions: Yup.bool().oneOf(
      [true],
      "You must agree to terms and conditions"
    ),
  });

  const handleSubmit = async (values, { resetForm }) => {
    try {

      setButtonSubmited(true);
      employmentInterestsOptions?.map((item) => {
        values[item.key] = values.employmentInterests.includes(item.value)
          ? true
          : false;
      });
      delete values.employmentInterests;
      delete values.verifyEmail;
      await contactUs(FORM_TYPES.EMPLOYMENT_OPPORTUNITY, values);

      toast({
        type: "success",
        message:
          "Thank you for your interest in working at URW Airports. We look forward to sharing hiring notices and connecting you with our construction and concessions partners.",
      });

      resetForm();
      setSubmited(true);
      setButtonSubmited(false);

    } catch (error) {
      setButtonSubmited(false);
      toast({ type: "error", message: "Something went wrong" });
    }
  };


  return (
    <>
      {!submited && (
        <Formik
          enableReinitialize={true}
          initialValues={initialValues}
          validationSchema={validationSchema}
          onSubmit={handleSubmit}
        >
          {({ handleChange, values, isSubmitting }) => {
            return (
              <Form className="business-form-box">
                <section className="business-form">
                  <div className="container">
                    <div className="title">
                      <h2>Want to work at the URW Airports?</h2>
                      <p>
                        {" "}
                        Sign up today to get hiring notices and be connected with
                        our construction and concessions partners.{" "}
                      </p>
                      <span className="span-subtitle-2">(Required*)</span>
                    </div>
                    <div className="row">
                      <div className="col col-6">
                        <div className="form-field">
                          <h4>Contact Information</h4>

                          <div className="form-group">
                            <Field
                              id="firstName"
                              type="text"
                              value={values.firstName}
                              onChange={handleChange}
                              className={`business-form-control ${values.firstName ? "has-value" : ""
                                }`}
                            />
                            <label>First Name *</label>
                          </div>
                          <ErrorMessage name="firstName" component={TextError} />
                          <div className="form-group">
                            <Field
                              id="lastName"
                              type="text"
                              value={values.lastName}
                              onChange={handleChange}
                              className={`business-form-control ${values.lastName ? "has-value" : ""
                                }`}
                            />
                            <label>Last Name *</label>
                          </div>
                          <ErrorMessage name="lastName" component={TextError} />

                          <div className="form-group">
                            <Field
                              id="email"
                              type="text"
                              value={values.email}
                              onChange={handleChange}
                              className={`business-form-control ${values.email ? "has-value" : ""
                                }`}
                            />
                            <label>Email *</label>
                          </div>

                          <ErrorMessage name="email" component={TextError} />
                          <div className="form-group">
                            <Field
                              id="verifyEmail"
                              type="text"
                              value={values.verifyEmail}
                              onChange={handleChange}
                              className={`business-form-control ${values.verifyEmail ? "has-value" : ""
                                }`}
                            />
                            <label>Verify Email *</label>
                          </div>
                          <ErrorMessage
                            name="verifyEmail"
                            component={TextError}
                          />
                          <div className="form-group">
                            <Field
                              id="mobile"
                              type="text"
                              value={values.mobile}
                              onChange={handleChange}
                              className={`business-form-control ${values.mobile ? "has-value" : ""
                                }`}
                            />
                            <label>Mobile Number *</label>
                          </div>
                          <ErrorMessage name="mobile" component={TextError} />
                        </div>
                        <div className="form-field">
                          <div className="form-group">
                            <Field
                              id="homeAddress1"
                              type="text"
                              value={values.homeAddress1}
                              onChange={handleChange}
                              className={`business-form-control ${values.homeAddress1 ? "has-value" : ""
                                }`}
                            />
                            <label>Home Address *</label>
                          </div>
                          <ErrorMessage
                            name="homeAddress1"
                            component={TextError}
                          />
                          <div className="form-group">
                            <Field
                              id="homeAddress2"
                              type="text"
                              value={values.homeAddress2}
                              onChange={handleChange}
                              className={`business-form-control ${values.homeAddress2 ? "has-value" : ""
                                }`}
                            />
                            <label>Home Address 2</label>
                          </div>

                          <div className="form-group">
                            <Field
                              id="city"
                              type="text"
                              value={values.city}
                              onChange={handleChange}
                              className={`business-form-control ${values.city ? "has-value" : ""
                                }`}
                            />
                            <label>City *</label>
                          </div>
                          <ErrorMessage name="city" component={TextError} />

                          <div className="two-colum">
                            <div className="form-group">
                              <select
                                className="business-form-control business-select"
                                defaultValue={values.state}
                                values={values?.state}
                                onChange={handleChange}
                              >
                                <option value="AL">Alabama</option>
                                <option value="AK">Alaska</option>
                                <option value="AZ">Arizona</option>
                                <option value="AR">Arkansas</option>
                                <option value="CA">California</option>
                                <option value="CO">Colorado</option>
                                <option value="CT">Connecticut</option>
                                <option value="DE">Delaware</option>
                                <option value="DC">District Of Columbia</option>
                                <option value="FL">Florida</option>
                                <option value="GA">Georgia</option>
                                <option value="HI">Hawaii</option>
                                <option value="ID">Idaho</option>
                                <option value="IL">Illinois</option>
                                <option value="IN">Indiana</option>
                                <option value="IA">Iowa</option>
                                <option value="KS">Kansas</option>
                                <option value="KY">Kentucky</option>
                                <option value="LA">Louisiana</option>
                                <option value="ME">Maine</option>
                                <option value="MD">Maryland</option>
                                <option value="MA">Massachusetts</option>
                                <option value="MI">Michigan</option>
                                <option value="MN">Minnesota</option>
                                <option value="MS">Mississippi</option>
                                <option value="MO">Missouri</option>
                                <option value="MT">Montana</option>
                                <option value="NE">Nebraska</option>
                                <option value="NV">Nevada</option>
                                <option value="NH">New Hampshire</option>
                                <option value="NJ">New Jersey</option>
                                <option value="NM">New Mexico</option>
                                <option value="NY">New York</option>
                                <option value="NC">North Carolina</option>
                                <option value="ND">North Dakota</option>
                                <option value="OH">Ohio</option>
                                <option value="OK">Oklahoma</option>
                                <option value="OR">Oregon</option>
                                <option value="PA">Pennsylvania</option>
                                <option value="RI">Rhode Island</option>
                                <option value="SC">South Carolina</option>
                                <option value="SD">South Dakota</option>
                                <option value="TN">Tennessee</option>
                                <option value="TX">Texas</option>
                                <option value="UT">Utah</option>
                                <option value="VT">Vermont</option>
                                <option value="VA">Virginia</option>
                                <option value="WA">Washington</option>
                                <option value="WV">West Virginia</option>
                                <option value="WI">Wisconsin</option>
                                <option value="WY">Wyoming</option>
                              </select>
                            </div>
                            <div className="form-group">
                              <Field
                                id="zipCode"
                                type="number"
                                value={values.zipCode}
                                onChange={handleChange}
                                className={`business-form-control ${values.zipCode ? "has-value" : ""
                                  }`}
                              />
                              <label>Zip Code</label>
                            </div>
                            <div> </div>
                            <ErrorMessage
                              name="zipCode"
                              component={TextError}
                            />
                          </div>
                        </div>
                      </div>
                      <div className="col col-6 col-6-border">
                        <div className="form-field">
                          <h4>Employment Interests</h4>
                          <h5>
                            I am interested in employment opportunities in (select
                            all that apply):
                          </h5>
                          <Field name="employmentInterests">
                            {({ field }) => {
                              return employmentInterestsOptions?.map((option) => {
                                return (
                                  <React.Fragment key={option.key}>
                                    <div className="checkbox">
                                      <input
                                        type="checkbox"
                                        className="form-control"
                                        id={option.key}
                                        {...field}
                                        value={option.value}
                                        checked={
                                          field?.value?.includes(option?.value) ||
                                          false
                                        }
                                      ></input>
                                      <label htmlFor={option.key}>
                                        {option.value}
                                      </label>
                                    </div>
                                  </React.Fragment>
                                );
                              });
                            }}
                          </Field>
                          <br />
                          <ErrorMessage
                            name="businessInterests"
                            component={TextError}
                          />
                        </div>

                        <div className="form-field">
                          <div className="form-group pt-30">
                            <Field
                              id="airportEmploymentExperience"
                              as="textarea"
                              value={values.airportEmploymentExperience}
                              onChange={handleChange}
                              className={`form-control ${values.airportEmploymentExperience ? "has-value" : ""
                                }`}
                              rows="4"
                            />
                            <label>
                              Do you have current or previous airport employment
                              experience?
                            </label>
                          </div>
                          <ErrorMessage
                            name="airportEmploymentExperience"
                            component={TextError}
                          />
                        </div>

                        <h4>I would like to receive JFK updates via:</h4>
                        <div className="checkbox">
                          <Field
                            type="checkbox"
                            id="emailUpdates"
                            name="emailUpdates"
                          />
                          <label htmlFor="emailUpdates">Email Updates</label>
                        </div>

                        <div className="checkbox">
                          <Field
                            type="checkbox"
                            id="advancedNetworkNews"
                            name="advancedNetworkNews"
                          />
                          <label htmlFor="advancedNetworkNews">
                            I'd also like to receieve occasional updates from URW
                            Airports,including information about the Advanced
                            Network News and opportunities.
                          </label>
                        </div>
                        <div className="checkbox">
                          <Field
                            type="checkbox"
                            id="termsAndConditions"
                            name="termsAndConditions"
                          />

                          <label htmlFor="termsAndConditions">
                            I've read to and agree to URW Airports' &nbsp;
                            <a
                              href="https://www.westfield.com/terms-and-conditions"
                              target="_blank"
                              rel="noreferrer"
                              className="link"
                            >
                              Terms of Use
                            </a>{" "}
                            and &nbsp;
                            <a
                              href="https://www.urw.com/Privacy-Policy"
                              target="_blank"
                              rel="noreferrer"
                              className="link"
                            >
                              Privacy Policy
                            </a>
                          </label>
                        </div>
                        <br />
                        <ErrorMessage
                          name="termsAndConditions"
                          component={TextError}
                        />
                        {/* <ReCAPTCHA
                        sitekey="6Ld_dAInAAAAAPsDMNQaDgnrSBmPYnrb4KVkmVIy"
                        onChange={onChange}
                        style={{ display: "inline-block" }}
                        ref={captchaRef}
                      /> */}
                        <div className="business-form-submit">
                          <input
                            type="submit"
                            className="btn-primary"
                            value={"submit"}
                            disabled={isSubmitting}

                          />
                          {buttonSubmited && (
                            <span>
                              <span className="loader"></span>
                            </span>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                </section>
              </Form>
            );
          }}
        </Formik>
      )}
      {submited && (
        <div className="thanks-msg">
          <div className="check-background">
            <svg
              viewBox="0 0 65 51"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M7 25L27.3077 44L58.5 7"
                stroke="white"
                strokeWidth="13"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
          </div>
          <h2>Thank You</h2>
          <p>You're On Our List!</p>
        </div>
      )}
    </>
  );
};

export default EmploymentOpportunitiesForm;
