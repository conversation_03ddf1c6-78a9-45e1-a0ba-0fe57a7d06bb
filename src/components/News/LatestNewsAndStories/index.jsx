import Link from "next/link";
import Slider from "react-slick";
const sliderSettings = {
  slidesToShow: 1,
};

const LatestNewsAndStories = ({ latestNewsandStories }) => {
  return (
    <>
      <section className="latest-news-section">
        <div className="container">
          <div className="sub-container">
            <h2>LATEST NEWS & TOP STORIES</h2>
            <Slider {...sliderSettings} className="latest-news-carousel">
              {latestNewsandStories?.items?.map((item, index) => {
                return (
                  <div className="news-item" key={index}>
                    <div className="news-img">
                      <Link href={item?.slug ? `/${item?.slug}/` : "#"}>
                        <img src={item?.bannerImage?.url} alt="" />
                      </Link>
                      {/* <img src={item?.bannerImage?.url} alt="" /> */}
                    </div>
                    <div className="news-info">
                      <h4>{item?.tags}</h4>
                      <h3>{item?.title}</h3>
                      <p> {item?.subTitle}</p>
                      <div className="news-btn">
                        <Link href={item?.slug ? `/${item?.slug}/` : "#"} className="btn">
                          Read More
                        </Link>
                      </div>
                    </div>
                  </div>
                );
              })}
            </Slider>
          </div>
        </div>
      </section>
    </>
  );
};

export default LatestNewsAndStories;
