import { useState, useEffect } from "react";
import styles from "./JFKEventsNews.module.scss"
import elasticlunr from "elasticlunr";
import React from "react";

const index = elasticlunr(function () {
  this.addField("title");
  this.addField("subTitle");
  this.addField("slug");
  this.addField("bannerImage");
  this.addField("tags");
  this.addField("newsPublishedDate");
  // this.addField("eventLink");
  this.addField("outreachVideo");
  // this.addField("createdAt");
  // this.addField("updatedAt");
  this.setRef("id");

});

const indexPast = elasticlunr(function () {
  this.addField("title");
  this.addField("subTitle");
  this.addField("slug");
  this.addField("bannerImage");
  this.addField("tags");
  this.addField("newsPublishedDate");
  // this.addField("eventLink");
  this.addField("outreachVideo");
  // this.addField("createdAt");
  // this.addField("updatedAt");
  this.setRef("id");
});



const toMonthShortFormat = (date) => {
  const monthNames = ["Jan", "Feb", "Mar", "Apr",
    "May", "Jun", "Jul", "Aug",
    "Sep", "Oct", "Nov", "Dec"];

  const monthIndex = date.getMonth();
  return monthNames[monthIndex].toUpperCase();
}

function formatAMPM(date) {
  var hours = date.getHours();
  var minutes = date.getMinutes();
  var ampm = hours >= 12 ? 'PM' : 'AM';
  hours = hours % 12;
  hours = hours ? hours : 12; // the hour '0' should be '12'
  minutes = minutes < 10 ? '0' + minutes : minutes;
  var strTime = hours + ':' + minutes + ' ' + ampm;
  return strTime;
}

const EventNews = ({ eventNews }) => {
  // console.log(eventNews)
  // const [eventsNews, setEventsNews] = useState([]);
  const [searchQuery, setSearchQuery] = useState("");

  const [open, setOpen] = useState(false);
  const [caption, setCaption] = useState("All");


  const handleCaptionClick = () => {
    setOpen((open) => !open);
  };


  useEffect(() => {
    search();
  }, []);

  const handleSearch = (e) => {
    e.preventDefault();
    search();
  };

  const search = () => {
    var currentarr = [];
    var pastarr = [];
    if (searchQuery) {
      currentarr = index
        .search(searchQuery, {
          fields: {
            title: { boost: 2 },
            body: { boost: 1 },
          },
        })
        .map((item) => {
          return index.documentStore.docs[item.ref];
        });

      pastarr = indexPast
        .search(searchQuery, {
          fields: {
            title: { boost: 2 },
            body: { boost: 1 },
          },
        })
        .map((item) => {
          return indexPast.documentStore.docs[item.ref];
        });


    } else {
      Object.keys(index.documentStore.docs).map((doc) => {
        currentarr.push(index.documentStore.docs[doc]);
      });

      Object.keys(indexPast.documentStore.docs).map((doc) => {
        pastarr.push(indexPast.documentStore.docs[doc]);
      });
    }
    currentarr = currentarr.sort(function (a, b) {
      return new Date(a.newsPublishedDate) - new Date(b.newsPublishedDate);
    });

    pastarr = pastarr.sort(function (a, b) {
      return new Date(b.newsPublishedDate) - new Date(a.newsPublishedDate);
    });
    setStaticEvents(currentarr);
    setPastEvents(pastarr)
  };


  const handleItemClick = (e) => {
    setCaption(e.target.innerHTML);
    setOpen(false);
  };

  const getCurrentEvents = () => {

    return eventNews?.items?.filter(x => {
      var now = new Date();
      // now = new Date(now.getTime() + now.getTimezoneOffset() * 60000)
      return new Date(x.newsPublishedDate) >= now;
    })
  }
  getCurrentEvents().forEach((event) => {

    var eventDate = new Date(event?.newsPublishedDate);
    var utc = new Date(eventDate.getTime() + eventDate.getTimezoneOffset() * 60000);
    index.addDoc({
      id: event?.sys.id,
      title: event?.title,
      subTitle: event?.subTitle,
      slug: event.slug,
      bannerImage: event?.bannerImage?.url,
      tags: event.tags,
      newsPublishedDate: utc
    });
  });

  var currentarr = [];
  Object.keys(index.documentStore.docs).map((doc) =>
    currentarr.push(index.documentStore.docs[doc])
  );
  currentarr = currentarr.sort(function (a, b) {
    return new Date(a.newsPublishedDate) - new Date(b.newsPublishedDate);
  });

  const [staticEvents, setStaticEvents] = useState(currentarr);


  const getCompletedEvents = () => {

    return eventNews?.items?.filter(x => {
      var now = new Date();
      // now = new Date(now.getTime() + now.getTimezoneOffset() * 60000)
      return new Date(x.newsPublishedDate) < now;
    })
  }
  getCompletedEvents().forEach((event) => {

    var eventDate = new Date(event?.newsPublishedDate);
    var utc = new Date(eventDate.getTime() + eventDate.getTimezoneOffset() * 60000);
    indexPast.addDoc({
      id: event?.sys.id,
      title: event?.title,
      subTitle: event?.subTitle,
      slug: event.slug,
      bannerImage: event?.bannerImage?.url,
      tags: event.tags,
      newsPublishedDate: utc,
      outreachVideo: event?.outreachVideo
    });
  });
  var completedarr = [];
  Object.keys(indexPast.documentStore.docs).map((doc) =>
    completedarr.push(indexPast.documentStore.docs[doc])
  );
  completedarr = completedarr.sort(function (a, b) {
    return new Date(a.newsPublishedDate) - new Date(b.newsPublishedDate);
  });

  const [pastEvents, setPastEvents] = useState(completedarr);
  // console.log(pastEvents);

  // useEffect(() => {
  //   // Filter news items with tag 'events'
  //   const filteredEventsNews = eventNews?.items.filter(
  //     (item) => item.tags === "Events"
  //   );
  //   setEventsNews(filteredEventsNews);
  // }, [eventNews]);

  const dateOptions = {
    year: "numeric",
    month: "long",
    day: "numeric",
  };

  return (

    <>
      <div className="filter-section">
        <div className="container">
          <div className="row">
            <div className="col col-6">
              <div className="left-part">
                <h2>Sort By</h2>
                <div className={`custom-dropdown ${open ? "open" : ""}`}>
                  <div className="caption" onClick={handleCaptionClick}>
                    {caption}
                  </div>
                  <div className="list">
                    <div className="item" onClick={handleItemClick}>
                      All
                    </div>
                    <div className="item" onClick={handleItemClick}>
                      Upcoming Events
                    </div>
                    <div className="item" onClick={handleItemClick}>
                      Past Events
                    </div>

                  </div>
                </div>
              </div>
            </div>
            <div className="col col-6">
              <form className="flex search-form form-group">
                <input
                  type="text"
                  placeholder="Search Events"
                  className="form-control"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
                <input
                  type="submit"
                  value="Search"
                  className="btn btn-secondary"
                  onClick={handleSearch}
                />
              </form>
            </div>
          </div>
        </div>
      </div>
      <section className={styles.eventsSection}>
        <div className="container">
          <div className="">
            {(caption == "All" || caption == "Upcoming Events") && staticEvents?.length > 0 ? (
              staticEvents?.map((event, key) => {

                return (
                  event && <React.Fragment key={key}>
                    <div className={styles.events}>
                      <div className={styles.eventsItem}>
                        <div className="row">
                          <div className="col col-5">
                            <div className={`${styles.eventFlex} flex`}>
                              <div className={styles.date}>
                                <h3>{toMonthShortFormat(event.newsPublishedDate)}</h3>
                                <h2>{event.newsPublishedDate.getDate()}</h2>
                              </div>
                              <div className={styles.img}>
                                <a href={`/${event.slug}`} target="_blank" rel="noreferrer">
                                  <img src={event.bannerImage} alt="" />
                                </a>
                              </div>
                            </div>
                          </div>
                          <div className="col col-7">
                            <div className={styles.info}>
                              <h3>
                                <a href={`/${event.slug}`} target="_blank" rel="noreferrer">
                                  {event.title}
                                </a>
                              </h3>
                              <h4>{formatAMPM(event.newsPublishedDate)} - {event.newsPublishedDate.toLocaleString("en-US", dateOptions)}</h4>
                              <p>
                                {event.description}
                              </p>
                              <div className={styles.action}>
                                <a href={`/${event.slug}`} target="_blank" className="item-link" rel="noreferrer">
                                  <span className="link">View Event Details</span>
                                  {/* <img
                                    src="/assets/images/icon-arrow.png"
                                    alt="icon-arrow"
                                  /> */}
                                </a>
                              </div>

                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </React.Fragment>
                );
              })
            ) : (
              <>
                {caption == "Upcoming Events" &&
                  <div className="form-section">
                    <div className="container">
                      <div className="form-text">
                        <h3>No Entries</h3>
                      </div>
                    </div>
                  </div>
                }
              </>
            )}

            {(caption == "All" || caption == "Past Events") && pastEvents?.length > 0 ? (
              <>
                {caption == "All" && <div className="title flex-space-between"><h2>Past Events</h2></div>}
                {
                  pastEvents?.map((event, key) => {

                    return (
                      event && <React.Fragment key={key}>
                        <div className={styles.events}>
                          <div className={styles.eventsItem}>
                            <div className="row">
                              <div className="col col-5">
                                <div className={`${styles.eventFlex} flex`}>
                                  <div className={styles.date}>
                                    <h3>{toMonthShortFormat(event.newsPublishedDate)}</h3>
                                    <h2>{event.newsPublishedDate.getDate()}</h2>
                                  </div>
                                  <div className={styles.img}>
                                    <a href={`/${event.slug}`} target="_blank" rel="noreferrer">
                                      <img src={event.bannerImage} alt="" />
                                    </a>
                                  </div>
                                </div>
                              </div>
                              <div className="col col-7">
                                <div className={styles.info}>
                                  <h3>
                                    <a href={`/${event.slug}`} target="_blank" rel="noreferrer">
                                      {event.title}
                                    </a>
                                  </h3>
                                  <h4>{formatAMPM(event.newsPublishedDate)} - {event.newsPublishedDate.toLocaleString("en-US", dateOptions)}</h4>
                                  <p>
                                    {event.description}
                                  </p>
                                  <div className={styles.action}>
                                    <a href={`/${event.slug}`} target="_blank" className="item-link" rel="noreferrer">
                                      <span className="link">View Event Details</span>
                                      {/* <img
                                        src="/assets/images/icon-arrow.png"
                                        alt="icon-arrow"
                                      /> */}
                                    </a>
                                  </div>
                                  {event.outreachVideo && <div className={styles.action}>
                                    <a href={event.outreachVideo} target="_blank" className="item-link" rel="noreferrer">
                                      <span className="link">Watch Outreach Video</span>
                                      {/* <img
                                        src="/assets/images/icon-arrow.png"
                                        alt="icon-arrow"
                                      /> */}
                                    </a>
                                  </div>}
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </React.Fragment>
                    );
                  })
                }
              </>

            ) : (
              <>
                {caption == "Past Entries" &&
                  <div className="form-section">
                    <div className="container">
                      <div className="form-text">
                        <h3>No entries</h3>
                      </div>
                    </div>
                  </div>
                }
              </>
            )}
          </div>
        </div>
      </section>
    </>

    // <section className="news-section">
    //   <div className="container">
    //     <div className="news-cards">
    //       {eventsNews?.map((item, i) => (
    //         <div className="news-item" key={i}>
    //           <div className="news-img">
    //             <Link href={`/${item.slug}/`}>
    //               <img src={item.bannerImage.url} alt="" />
    //             </Link>
    //           </div>
    //           <div className="news-info">
    //             <h4>{item.tags}</h4>
    //             <h3>
    //               <Link href={`/${item.slug}/`}>{item.title}</Link>
    //             </h3>
    //             <h5>
    //               {new Date(item.newsPublishedDate).toLocaleString(
    //                 "en-US",
    //                 dateOptions
    //               )}
    //             </h5>
    //             <p>
    //               {item.subTitle && item.subTitle.length > 80
    //                 ? item.subTitle.slice(0, 80) + "..."
    //                 : item.subTitle}
    //             </p>
    //             <div className="news-link">
    //               <Link href={`/${item.slug}/`} className="link">
    //                 Read More
    //               </Link>
    //             </div>
    //           </div>
    //         </div>
    //       ))}
    //     </div>
    //   </div>
    // </section>
  );
};

export default EventNews;
