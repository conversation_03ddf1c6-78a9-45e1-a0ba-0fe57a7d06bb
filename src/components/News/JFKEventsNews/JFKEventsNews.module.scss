@import "../../../../styles/variables.scss";

.eventsSection {
    background: url(/assets/images/shadow-bottom.png) no-repeat center top;
    padding: 80px 0 60px;
    @media screen and (max-width: 767px) {
      padding: 40px 0 20px;
    }
  }
  .events {
    .eventsItem {
      border-bottom: solid 1px #dcdcdc;
      padding-bottom: 40px;
      margin-bottom: 40px;
    }
    .eventFlex {
      align-items: flex-start;
      position: relative;
      flex-wrap: nowrap;
    }
    .date {
      text-align: center;
      h3 {
        font-weight: 400;
        position: relative;
        padding-bottom: 12px;
        margin-bottom: 15px;
        &::after {
          content: "";
          position: absolute;
          left: 50%;
          bottom: 0;
          width: 22px;
          height: 1px;
          margin-left: -11px;
          background-color: #808080;
        }
      }
    }
    .img {
      width: 100%;
      height: 300px;
      overflow: hidden;
      margin-right: 4px;
      img {
        object-fit: cover;
        width: 100%;
        height: 100%;
      }
    }
    .info {
      h3 {
        font-weight: 400;
      }
      h4 {
        font-weight: 500;
        color: #808080;
        margin: 0;
      }
      .action {
        margin-top: 16px;
        a {
          display: flex;
          align-items: center;
          
          img {
            height: 16px;
            padding-left: 10px;
          }
          &:hover{
            span {
              color: $primary;
            }
          }
        }
      }
    }
  
    @media screen and (min-width: 768px) {
      .date {
        padding-right: 40px;
        h2 {
          font-size: 46px;
        }
        h3 {
          font-size: 28px;
        }
      }
      .info {
        h3 {
          font-size: 28px;
          line-height: 40px;
        }
        h4 {
          font-size: 18px;
        }
        p {
          font-size: 18px;
        }
        .action {
          margin-top: 25px;
          a {
            font-size: 16px;
          }
          img {
            height: 22px;
            padding-left: 10px;
            transition: all 0.3s ease;
          }
          &:hover {
            img {
              padding-left: 15px;
            }
          }
        }
      }
    }
  
    @media screen and (max-width: 991px) {
      .eventsItem {
        padding-bottom: 30px;
        margin-bottom: 30px;
        div {
          width: 100%;
          margin: 0;
          padding: 0;
        }
        .date {
          width: auto;
          position: absolute;
          background: rgba(255, 255, 255, 0.9);
          padding: 10px 8px;
          left: 10px;
          top: 10px;
          h3 {
            padding-bottom: 8px;
            margin-bottom: 10px;
            font-size: 16px;
            line-height: 1;
          }
          h2 {
            font-size: 20px;
            line-height: 1;
            margin: 0;
          }
        }
        .img {
          margin: 0 0 20px;
        }
      }
    }
  
    @media screen and (max-width: 767px) {
      .img {
        height: 200px;
      }
      h4{
        font-size: 14px;
      }
    }
  }
  