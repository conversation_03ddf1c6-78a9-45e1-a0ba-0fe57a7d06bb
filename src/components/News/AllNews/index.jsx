import { useState, useEffect } from "react";
import Link from "next/link";
import { useRouter } from "next/router";
import NewsCard from "../NewsCard";
const AllNews = ({ news, currentPage }) => {
  const router = useRouter();
  const newsPerRow = 9;
  const intialNewsCount = 15;


  const [list, setList] = useState(news?.items);
  const [index, setIndex] = useState(intialNewsCount);
  // const [activeTab, setActiveTab] = useState("ALL");
  const [activeTag, setActiveTag] = useState("All");
  const [toggle, setToggle] = useState(false);

  // const TABS = {
  //   ALL: "ALL",
  //   NEW_YORK: "NEW YORK",
  //   LOS_ANGELES: "LOS ANGELES",
  //   CHICAGO: "CHICAGO",
  //   ADVANCE_NETWORK: "ADVANCE NETWORK",
  // };

  const TAGS = {
    ALL: "All",
    ADVANCE_NETWORK: "Advance Network",
    AWARDS_AND_RECOGNITION: "Awards & Recognition",
    BUSINESS: "Business",
    COMMUNITY: "Community",
    CUSTOMER_EXPERIENCE: "Customer Experience",
    DESIGN: "Design",
    INNOVATION: "Innovation",
    LEASING: "Leasing",
    ENVIRONMENT: "Environment",
    SUSTAINABLE_EXPERIENCE: "Sustainable Experience"
  };

  useEffect(() => {
    if (router.asPath.indexOf("?tag=") > 0 && router.asPath.indexOf("/advance-network/") == -1) {
      const tag = router.asPath.match(/tag=([^&]*)/)[1];
      switch (tag) {
        case "advance-network":
          setActiveTag(TAGS.ADVANCE_NETWORK);
          break;
        case "awards-and-recognition":
          setActiveTag(TAGS.AWARDS_AND_RECOGNITION);
          break;
        case "business":
          setActiveTag(TAGS.BUSINESS);
          break;
        case "community":
          setActiveTag(TAGS.COMMUNITY);
          break;
        case "customer-experience":
          setActiveTag(TAGS.CUSTOMER_EXPERIENCE);
          break;
        case "design":
          setActiveTag(TAGS.DESIGN);
          break;
        case "innovation":
          setActiveTag(TAGS.INNOVATION);
          break;
        case "leasing":
          setActiveTag(TAGS.LEASING);
          break;
        case "environment":
          setActiveTag(TAGS.ENVIRONMENT);
          break;
        case "sustainable-experience":
          setActiveTag(TAGS.SUSTAINABLE_EXPERIENCE);
          break;
        default:
          setActiveTag("All");
          break;
      }
      // setActiveTag(TAGS.COMMUNITY);
    } else if (router.asPath.indexOf("/environment/") > 0) {
      setActiveTag(TAGS.ENVIRONMENT);
    }
    else if (router.asPath.indexOf("/sustainable-experience/") > 0) {
      setActiveTag(TAGS.SUSTAINABLE_EXPERIENCE);
    }
    else {
      setActiveTag("All");
    }
    setIndex(intialNewsCount);
    setToggle(false);
    setList(news?.items);
  }, [router.asPath]);

  const filterNews = () => {
    let filteredNews = news?.items;

    // if (activeTag == TAGS.ALL) {
    //   filteredNews = news?.items;
    // }

    if (activeTag != TAGS.ALL) {
      filteredNews = news?.items?.filter((m) => m.tags == activeTag);
    }

    // if (activeTab != TABS.ALL && activeTag == TAGS.ALL) {
    //   if (activeTab == TABS.ADVANCE_NETWORK) {
    //     filteredNews = news?.items?.filter((m) => m.tags == TAGS.ADVANCE_NETWORK);
    //   } else {
    //     filteredNews = news?.items?.filter((m) => m.airport == activeTab);
    //   }
    // }

    // if (activeTab != TABS.ALL && activeTag != TAGS.ALL) {
    //   if (activeTab == TABS.ADVANCE_NETWORK) {
    //     filteredNews = news?.items?.filter((m) => m.tags == TAGS.ADVANCE_NETWORK);
    //   } else {
    //     filteredNews = news?.items?.filter((m) => m.airport == activeTab && m.tags == activeTag);
    //   }
    // }

    setList(filteredNews);
    setIndex(intialNewsCount);
  };

  useEffect(() => {
    filterNews();
  }, [activeTag]);

  const loadMore = () => {
    setIndex(index + newsPerRow);
  };

  const handleTabClick = (e) => {
    // setActiveTab(tab);
    e.preventDefault();
    setToggle((toggle) => !toggle);
  };

  return (
    <section className="tabs-section news-tabs-section">
      <div className="container">
        <div className="tabs-container">
          <nav className="tabs">
            <ul className={toggle ? "expanded" : ""}>
              <li className={(!currentPage || currentPage == "environment" || currentPage == "sustainable-experience") ? "active" : ""} onClick={handleTabClick}>
                <Link href="/news/" style={!currentPage ? { pointerEvents: "none" } : {}}>All</Link>
                <span></span>
              </li>
              {/* <li className={activeTab == TABS.ALL ? "active" : ""} onClick={() => handleTabClick(TABS.ALL)} data-tab="tab-1">
                All
                <span></span>
              </li> */}
              <li className={currentPage == "new-york" ? "active" : ""} onClick={handleTabClick}>
                <Link href="/news/new-york/" style={currentPage == "new-york" ? { pointerEvents: "none" } : {}}>NEW YORK</Link>
                <span></span>
              </li>
              {/* <li data-tab="tab-2" className={activeTab == TABS.NEW_YORK ? "active" : ""} onClick={() => handleTabClick(TABS.NEW_YORK)}>
                NEW YORK
                <span></span>
              </li> */}
              <li className={currentPage == "los-angeles" ? "active" : ""} onClick={handleTabClick}>
                <Link href="/news/los-angeles/" style={currentPage == "los-angeles" ? { pointerEvents: "none" } : {}}>LOS ANGELES</Link>
                <span></span>
              </li>

              {/* <li data-tab="tab-3" className={activeTab == TABS.LOS_ANGELES ? "active" : ""} onClick={() => handleTabClick(TABS.LOS_ANGELES)}>
                LOS ANGELES
                <span></span>
              </li> */}
              {/* <li data-tab="tab-4" className={activeTab == TABS.CHICAGO ? "active" : ""} onClick={() => handleTabClick(TABS.CHICAGO)}>
                CHICAGO
                <span></span>
              </li> */}

              <li className={currentPage == "chicago" ? "active" : ""} onClick={handleTabClick}>
                <Link href="/news/chicago/" style={currentPage == "chicago" ? { pointerEvents: "none" } : {}}>CHICAGO </Link>
                <span></span>
              </li>

              <li className={currentPage == "advance-network" ? "active" : ""} onClick={handleTabClick}>
                <Link href="/news/advance-network/" style={currentPage == "advance-network" ? { pointerEvents: "none" } : {}}>ADVANCE NETWORK</Link>
                <span></span>
              </li>
              {/* <li data-tab="tab-5" className={activeTab == TABS.ADVANCE_NETWORK ? "active" : ""} onClick={() => handleTabClick(TABS.ADVANCE_NETWORK)}>
                ADVANCE NETWORK
                <span></span>
              </li> */}
            </ul>
          </nav>
        </div>
        <div className="tabs-content">
          <div className="tab-content tab-1 current">
            {!(currentPage == "advance-network" || currentPage == "sustainable-experience" || currentPage == "environment") && (
              <div className="news-tags">
                <a
                  className={`tag ${activeTag == TAGS.ALL ? "current" : ""}`}
                  onClick={() => setActiveTag(TAGS.ALL)}
                >
                  All
                </a>
                <a
                  className={`tag ${activeTag == TAGS.ADVANCE_NETWORK ? "current" : ""
                    }`}
                  onClick={() => setActiveTag(TAGS.ADVANCE_NETWORK)}
                >
                  Advance Network
                </a>
                <a
                  className={`tag ${activeTag == TAGS.AWARDS_AND_RECOGNITION ? "current" : ""
                    }`}
                  onClick={() => setActiveTag(TAGS.AWARDS_AND_RECOGNITION)}
                >
                  Awards & Recognition
                </a>
                <a
                  className={`tag ${activeTag == TAGS.BUSINESS ? "current" : ""
                    }`}
                  onClick={() => setActiveTag(TAGS.BUSINESS)}
                >
                  Business
                </a>
                <a
                  className={`tag ${activeTag == TAGS.COMMUNITY ? "current" : ""
                    }`}
                  onClick={() => setActiveTag(TAGS.COMMUNITY)}
                >
                  Community
                </a>
                <a
                  className={`tag ${activeTag == TAGS.CUSTOMER_EXPERIENCE ? "current" : ""
                    }`}
                  onClick={() => setActiveTag(TAGS.CUSTOMER_EXPERIENCE)}
                >
                  Customer Experience
                </a>
                <a
                  className={`tag ${activeTag == TAGS.DESIGN ? "current" : ""}`}
                  onClick={() => setActiveTag(TAGS.DESIGN)}
                >
                  Design
                </a>
                <a
                  className={`tag ${activeTag == TAGS.INNOVATION ? "current" : ""
                    }`}
                  onClick={() => setActiveTag(TAGS.INNOVATION)}
                >
                  Innovation
                </a>
                <a
                  className={`tag ${activeTag == TAGS.LEASING ? "current" : ""
                    }`}
                  onClick={() => setActiveTag(TAGS.LEASING)}
                >
                  Leasing
                </a>
                <a
                  className={`tag ${activeTag == TAGS.ENVIRONMENT ? "current" : ""
                    }`}
                  onClick={() => setActiveTag(TAGS.ENVIRONMENT)}
                >
                  Environment
                </a>
                <a
                  className={`tag ${activeTag == TAGS.SUSTAINABLE_EXPERIENCE ? "current" : ""
                    }`}
                  onClick={() => setActiveTag(TAGS.SUSTAINABLE_EXPERIENCE)}
                >
                  Sustainable Experience
                </a>
              </div>
            )}

            <div className="news-cards">
              {list.slice(0, index)?.map((item, i) => {
                return (
                  <NewsCard item={item} key={i} />
                );
              })}
            </div>
            <div className="news-btn">
              {index < list?.length && (
                <a className="btn" onClick={loadMore}>
                  LOAD MORE
                </a>
              )}
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default AllNews;
