import NewsCard from "../NewsCard"
const TerminalOneNews = ({ news }) => {
    return (
        <>
            {
                news?.items?.length > 0 ? (<div className="news-cards">
                    {news?.items?.map((item, i) => {
                        return (
                            <NewsCard item={item} key={i} />
                        )
                    })}
                </div>) : (<div className="title"><h2>No News Found</h2></div>)
            }
        </>
    )

}

export default TerminalOneNews