import Link from "next/link";
const NewsCard = ({item}) => {

    const dateOptions = {
        year: "numeric",
        month: "long",
        day: "numeric",
      };
          return (
            <div className="news-item">
              <div className="news-img">
                <Link href={`/${item?.slug}/`}>
                  <img src={item?.bannerImage?.url} alt="" />
                </Link>
              </div>
              <div className="news-info">
                <h4>{item?.tags}</h4>
                <h3>
                  <Link href={`/${item?.slug}/`}>{item?.title}</Link>
                  {/* <a href="#">{item?.title}</a> */}
                </h3>
                <h5>
                  {new Date(item?.newsPublishedDate).toLocaleString(
                    "en-US",
                    dateOptions
                  )}
                </h5>
                <p>
                  {item?.subTitle?.length || 0 > 80
                    ? item?.subTitle?.slice(0, 80) + "..."
                    : item?.subTitle}
                </p>
                <div className="news-link">
                  <Link href={`/${item?.slug}/`} className="link">
                    Read More
                  </Link>
                </div>
              </div>
            </div>
          );
       
      
    
}

export default NewsCard;