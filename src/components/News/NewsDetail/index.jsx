import Slider from "react-slick";
import { INLINES, BLOCKS } from "@contentful/rich-text-types";
import React from "react";
import { documentToReactComponents } from "@contentful/rich-text-react-renderer";
const sliderSettings = {
  slidesToShow: 1,
  dots: true,
};

const renderOptions = (links) => {
  return {
    renderNode: {
      [INLINES.HYPERLINK]: (node) => {
        if (node.data.uri.includes("youtube.com/embed") || node.data.uri.includes("player.vimeo.com/video")) {
          return (
            <span className="video">
              <iframe
                width="600"
                height="400"
                src={node?.data?.uri}
                title="Video player"
                frameBorder="0"
                allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
                allowFullScreen
              ></iframe>
            </span>
          );
        } else {
          return (
            <a href={node?.data?.uri} className="link" target="_blank">
              {node?.content[0]?.value}
            </a>
          );
        }
      },
      // [BLOCKS.LIST_ITEM]: (node) => <li>{node.content[0].content[0].value}</li>,
      [BLOCKS.EMBEDDED_ASSET]: renderAsset(links?.assets?.block),
    },
  };
};

const renderAsset = (links) => (node) => {
  const nodeAssetId = node.data.target.sys.id;
  const asset = links.filter((x) => x.sys.id == nodeAssetId);
  if (!asset && asset.length == 0) return <></>;
  return (
    <p>
      <img src={asset[0].url} alt={asset[0].title}></img>
    </p>
  );
};

const options = {
  renderNode: {
    [INLINES.HYPERLINK]: (node) => {
      if (node.data.uri.includes("youtube.com/embed")) {
        return (
          <span className="video">
            <iframe
              width="600"
              height="400"
              src={node?.data?.uri}
              title="YouTube video player"
              frameBorder="0"
              allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
              allowFullScreen
            ></iframe>
          </span>
        );
      } else {
        return (
          <a href={node?.data?.uri} className="link" target="_blank">
            {node?.content[0]?.value}
          </a>
        );
      }
    },
    [BLOCKS.LIST_ITEM]: (node) => <li>{node.content[0].content[0].value}</li>,
  },
};
function socialWindow(url) {
  var left = (screen.width - 570) / 2;
  var top = (screen.height - 570) / 2;

  var params = "menubar=no,toolbar=no,status=no,width=570,height=570,top=" + top + ",left=" + left;

  window.open(url, "New Window", params);
}

const handleTwitterShare = () => {
  var url = "https://twitter.com/intent/tweet?url=" + encodeURI(window.location.href);
  socialWindow(url);
};

const handleLinkedInShare = () => {
  var url = "https://www.linkedin.com/shareArticle?mini=true&url=" + encodeURI(window.location.href);
  socialWindow(url);
};

const NewsDetail = ({ newsDetail }) => {
  const detail = newsDetail?.items[0];

  const dateOptions = {
    year: "numeric",
    month: "long",
    day: "numeric",
  };
  return (
    <>
      <div className="single-page">
        <section className="single-banner-section">
          <div className="container">
            <img src={detail?.bannerImage?.url} alt="" />
          </div>
        </section>

        <section className="single-detail-section">
          <div className="container">
            <div className="single-info">
              <h4>{detail?.tags}</h4>
              <h1>{detail?.title}</h1>
              <p>{detail?.subTitle}</p>
              <div className="single-share">
                {/* <div className="date">{new Date(detail?.newsPublishedDate).toLocaleString("en-US", dateOptions)}</div> */}
                <div className="share">
                  <a onClick={handleTwitterShare} className="twitter">
                    <img src="/assets/images/icon-twitter.svg" alt="" />
                  </a>
                  <a onClick={handleLinkedInShare} className="linkedIn">
                    <img src="/assets/images/icon-in.svg" alt="" />
                  </a>
                </div>
              </div>
            </div>
            <div className="single-content">
              {documentToReactComponents(detail?.contentBlock?.json, renderOptions(detail?.contentBlock?.links))}
              {(detail?.quoteText || detail?.quoteImage) && (
                <div className="quotes">
                  <img src={detail?.quoteImage?.url} alt="" />
                  <q>{detail?.quoteText}</q>
                </div>
              )}
              {documentToReactComponents(detail?.contentBlock2?.json, renderOptions(detail?.contentBlock2?.links))}

              {detail?.carouselCollection?.items?.length > 0 && (
                <Slider {...sliderSettings} className="single-carousel">
                  {detail?.carouselCollection?.items?.map((item, index) => {
                    return (
                      <React.Fragment key={index}>
                        <img src={item?.carouselImage?.url} alt="" />
                      </React.Fragment>
                    );
                  })}
                </Slider>
              )}
              {documentToReactComponents(detail?.contentBlock3?.json, renderOptions(detail?.contentBlock3?.links))}
            </div>
          </div>
        </section>
      </div>
    </>
  );
};

export default NewsDetail;
