import { documentToReactComponents } from "@contentful/rich-text-react-renderer";
import { INLINES } from "@contentful/rich-text-types"

const Banner = ({ banner }) => {
  const { items } = banner;

  const renderOptions = {
    renderNode: {
      [INLINES.HYPERLINK]: (node) => {
        return <a href={node.data.uri} target={`${node.data.uri.startsWith("https://") ? '_blank' : ''}`}>{node.content[0].value}</a>;
      }
    }
  }

  return (
    <>
      <section className="small-banner-section">
        <div className="container">
          <div className="banner-wrapper">
            <img src={items && items.length > 0 && items[0]?.bannerImage?.url} alt="" />
            <div className="banner-text">
              {documentToReactComponents(items && items.length > 0 && items[0]?.title?.json)}
              <a href={items && items[0]?.link ? items && items[0]?.link : "#"} target="_blank" className="btn">
                  <span>{items && items.length > 0 && items[0]?.linkText}</span> <i></i>
                </a>
              {/* <button className="btn modal-toggle">
                <span>{items && items.length > 0 && items[0]?.linkText}</span>
              </button> */}
            </div>
          </div>
        </div>
      </section>
    </>
  );
};

export default Banner;
