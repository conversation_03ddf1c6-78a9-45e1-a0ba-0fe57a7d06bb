@import "../../../../styles/variables.scss";
.premier-section {
  padding: 48px 0;
  margin-top: 70px;
  position: relative;
  &::before {
    content: "";
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 50%;
    background-color: $gray04;
    z-index: -1;
  }
  h2 {
    max-width: 700px;
    margin: 0 auto;
  }
  @media screen and (max-width: 767px) {
    margin: 50px 0 0;
    padding: 40px 0 20px;
    .title {
      margin-bottom: 20px;
    }
    .premier-carousel {
      padding-bottom: 30px;
    }
  }
}

.premier-carousel {
  padding-bottom: 50px;
  .premier-item {
    position: relative;
    padding: 0 12px;
    span {
      position: absolute;
      left: 12px;
      bottom: 0;
      padding: 16px 32px;
      background-color: $gray01;
      @media screen and (max-width: 767px) {
        padding: 12px 24px;
      }
    }
  }
}
