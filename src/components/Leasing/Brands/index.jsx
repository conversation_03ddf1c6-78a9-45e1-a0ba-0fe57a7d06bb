import Slider from "react-slick";
import styles from "./Brands.module.scss";
const Brands = ({ brands, carouselTitle }) => {
  const { items } = brands;

  const sliderSettings = {
    dots: true,
    arrows: false,
    centerMode: true,
    speed: 300,
    slidesToShow: 4,
    slidesToScroll: 4,
    responsive: [
      {
        breakpoint: 1200,
        settings: {
          slidesToShow: 3,
          slidesToScroll: 3,
        },
      },
      {
        breakpoint: 992,
        settings: {
          slidesToShow: 2,
          slidesToScroll: 2,
        },
      },
      {
        breakpoint: 667,
        settings: {
          slidesToShow: 1,
          slidesToScroll: 1,
          centerMode: false,
        },
      },
    ],
  };
  return (
    <>
      <section className={styles["premier-section"]}>
        <div className="container">
          <div className="title">
            <h2>{carouselTitle || (items && items.length > 0 && items[0]?.title)}</h2>
          </div>
        </div>
        <Slider {...sliderSettings} className={styles["premier-carousel"]}>
          {items &&
            items.length > 0 &&
            items[0]?.imageCollection?.items?.map((item, index) => {
              return (
                <div className={styles["premier-item"]} key={index}>
                  <img src={item?.url} alt="" />
                  <span>{item?.description}</span>
                </div>
              );
            })}
        </Slider>
      </section>
    </>
  );
};

export default Brands;
