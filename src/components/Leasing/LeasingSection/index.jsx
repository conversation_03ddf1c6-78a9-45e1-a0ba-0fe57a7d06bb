import { documentToReactComponents } from "@contentful/rich-text-react-renderer";
const LeasingSection = ({ section }) => {
  const { items } = section;
  return (
    <section className="leasing-section">
      <div className="container">
        <div className="sub-container">
          {items && items.length > 0 && <h2>{items[0]?.title}</h2>}
          {items && items.length > 0 && items[0]?.title2 && <h2>{items[0]?.title2}</h2>}
          <div className="sub-info">
            {documentToReactComponents(items && items.length > 0 && items[0]?.description?.json)}
            <div className="sub-btn">
              <a href={(items && items.length > 0 && items[0]?.link) || "#"} className="btn">
                {items && items.length > 0 && items[0]?.linkText}
              </a>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default LeasingSection;
