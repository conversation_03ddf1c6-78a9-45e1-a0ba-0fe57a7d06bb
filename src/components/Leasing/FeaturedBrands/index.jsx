import styles from "./FeaturedBrands.module.scss";
const FeaturedBrands = ({ featuredBrands }) => {
  const { items } = featuredBrands;
  return (
    <section className={styles["brands-section"]}>
      <div className={styles["container"]}>
        <div className="title">
          <h2>
            {items && items[0]?.title} <br />
            {items && items[0]?.title2}
          </h2>
        </div>
        <ul className={styles["logo-list"]}>
          {items &&
            items[0]?.logosCollection?.items?.map((item, index) => {
              return (
                <li key={index}>
                  <img src={item?.logo?.url} alt="" />
                </li>
              );
            })}
        </ul>
      </div>
    </section>
  );
};

export default FeaturedBrands;
