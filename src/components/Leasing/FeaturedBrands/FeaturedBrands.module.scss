@import "../../../../styles/variables.scss";
@import "../../../../styles/mixins.scss";
.brands-section {
  margin: 96px 0 0 0;
  .container {
    position: relative;
    padding-bottom: 64px;
    max-width: 1230px;
    margin: 0 auto;
    padding-left: 15px;
    padding-right: 15px;
    &::after {
      content: "";
      position: absolute;
      left: 15px;
      right: 15px;
      bottom: 0;
      width: calc(100% - 30px);
      border-bottom: solid 1px $gray10;
    }
  }
  @media screen and (max-width: 767px) {
    margin: 50px 0 0;
    .container {
      padding-bottom: 48px;
    }
    .logo-list {
      margin: 0 -3px;
      li {
        width: calc(33.33% - 6px);
        margin: 3px;
        padding: 10px;
        height: 64px;
        img {
          max-height: 60%;
        }
      }
    }
  }
}

.logo-list {
  @include no-list;
  display: flex;
  flex-wrap: wrap;
  margin: 0 -9px;
  li {
    border: solid 1px $gray01;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
    height: 125px;
    width: calc(20% - 18px);
    margin: 9px;
    @include transition(all);
    img {
      filter: grayscale(100%);
      -webkit-filter: grayscale(100%);
      transition: filter 600ms ease;
      -webkit-transition: -webkit-filter 600ms ease;
    }
    &:hover {
      border-color: $gray02;
      img {
        filter: grayscale(0);
        -webkit-filter: grayscale(0);
      }
    }
  }
}
