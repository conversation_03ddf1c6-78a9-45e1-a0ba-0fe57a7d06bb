import { documentToReactComponents } from "@contentful/rich-text-react-renderer";
import { BLOCKS } from "@contentful/rich-text-types";
const LeasingCardWithVideoOrImage = ({ leasingPageCardWithVideoOrImage }) => {
  const { items } = leasingPageCardWithVideoOrImage;

  const options = {
    renderNode: {
      [BLOCKS.UL_LIST]: (node, children) => <ul className="list">{children}</ul>,
      [BLOCKS.LIST_ITEM]: (node) => <li>{node.content[0].content[0].value}</li>,
    },
  };

  const handleClick = (e) => {
    const modalOverlay = document.querySelectorAll(["div.ReactModal__Overlay"])
    modalOverlay[0].classList.add("ReactModal__FadeIn");
    e.preventDefault();
  }

  return (
    <section className="leasing-zigzag-section">
      <div className="container">
        <div className="leasing-zigzag-info">
          {documentToReactComponents(items && items.length > 0 && items[0]?.title?.json)}
          <p>{items && items.length > 0 && items[0]?.description}</p>
        </div>
        <div className="flex">
          <div className="zigzag-video">
            <iframe
              width="600"
              height="400"
              src={items && items.length > 0 && items[0]?.card?.videoLink}
              title="YouTube video player"
              frameBorder="0"
              allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
              allowFullScreen=""
            ></iframe>
          </div>
          <div className="zigzag-info">
            <h3>{items && items.length > 0 && items[0]?.card?.title}</h3>
            {documentToReactComponents(items && items.length > 0 && items[0]?.card?.content.json, options)}

            <div className="zigzag-btn">
              {items && items.length > 0 && items[0]?.card?.link ? (
                <>
                  <a href={items[0]?.card?.link} className="btn">
                    <span>{items && items.length > 0 && items[0]?.card?.linkText}</span>
                  </a>
                </>
              ) : (
                <>
                  <a href="" className="btn" onClick={handleClick}>
                    <span>{items && items.length > 0 && items[0]?.card?.linkText}</span>
                  </a>
                </>
              )}
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default LeasingCardWithVideoOrImage;
