
const WhereWeFocus = ({ whereWeFocus }) => {
    return (
        <>
            <section className="zigzag-section">
                <div className="container">
                    <div className="title">
                        <h2>Where We focus</h2>
                    </div>

                    {whereWeFocus?.items?.map((item, index) => {
                        return (
                            <div className={index % 2 == 0 ? "flex" : "flex-reverse"} key={index}>
                                <div className="zigzag-img">
                                    <img src={item?.image?.url} alt="" />
                                </div>
                                <div className="zigzag-info">
                                    <h3>
                                        <img src="/assets/images/betterplaces-icon3.png" alt="" />
                                        <span>{item?.title}</span>
                                    </h3>
                                    <p>{item?.description}</p>
                                    <div className="zigzag-btn">
                                        <a href={item?.link ? item?.link : "#"} className="btn">
                                            {item?.linkText}
                                        </a>
                                    </div>
                                </div>
                            </div>
                        );
                    })}


                </div>
            </section>
        </>
    );
};

export default WhereWeFocus;
