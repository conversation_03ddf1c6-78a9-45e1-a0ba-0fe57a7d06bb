const YOUR_URL = process.env.SITE_URL;
const getDate = new Date().toISOString();
const fs = require("fs");
const globby = require("globby");

async function generateSiteMap() {
  let newsPaths = [];
  let portfoliosPath = [];
  const news = await GetNews();
  const portfolios = await GetPortfolios();

  news?.items?.map((item) => {
    return newsPaths.push({ slug: item.slug });
  });

  portfolios?.items?.map((item) => {
    return portfoliosPath.push({ slug: item.slug });
  });

  const newsList = `
  ${newsPaths
    .map((item) => {
      return `
        <url>
          <loc>${`${YOUR_URL}/${item.slug}/`}</loc>
          <lastmod>${getDate}</lastmod>
        </url>`;
    })
    .join("")}
`;

  const portfolioList = `
  ${portfoliosPath
    .map((item) => {
      return `
        <url>
          <loc>${`${YOUR_URL}/portfolio/${item.slug}/`}</loc>
          <lastmod>${getDate}</lastmod>
        </url>`;
    })
    .join("")}
`;

  // Send a list of paths to globby for it to read
  // We add an ! before the files we want to be ignored
  // change the file path to match the files in your own project
  let pages = await globby(["pages/**/*.js", "!pages/[news-details].js", "!pages/_*.js", "!pages/api", "!pages/404.js", "!pages/portfolio/[details].js"]);

  pages = pages.filter((page) => page != "pages/[news-details].js");

  const sitemap = `<?xml version="1.0" encoding="UTF-8"?>
      <urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9" xmlns:xhtml="http://www.w3.org/1999/xhtml">
          ${pages
            .map((page) => {
              const regex = /(.pages)|(pages)|(src)|(.jsx)|(.js)|(.md)|(.index)/gi;
              let route = page.replace(regex, "");
              return `
                      <url>
                          <loc>${`${YOUR_URL}${route.toLowerCase()}`}</loc>
                          <lastmod>${getDate}</lastmod>
                      </url>
                  `;
            })
            .join("")}
            ${newsList}
            ${portfolioList}
      </urlset>
  `;

  fs.writeFileSync("public/assets/sitemap.xml", sitemap);
}

const callContentful = async (query) => {
  // const fetchUrl = `https://graphql.contentful.com/content/v1/spaces/${process.env.CONTENTFUL_SPACE_ID}`;
  const fetchUrl = `https://graphql.contentful.com/content/v1/spaces/${process.env.CONTENTFUL_SPACE_ID}/environments/${process.env.CONTENTFUL_ENVIRONMENT}`;

  const fetchOptions = {
    method: "POST",
    headers: {
      Authorization: `Bearer ${process.env.CONTENTFUL_ACCESS_TOKEN}`,
      "Content-Type": "application/json",
    },
    body: JSON.stringify({ query }),
  };

  try {
    const data = await fetch(fetchUrl, fetchOptions).then((response) => response.json());
    return data;
  } catch (error) {
    throw new Error("Could not fetch data from Contentful!");
  }
};

// Get News
const GetNews = async () => {
  const newsQuery = `{
    newsCollection(order: newsPublishedDate_DESC) {
      items {
        slug
      }
    }
  }`;

  const response = await callContentful(newsQuery);

  const news = response.data.newsCollection ? response.data.newsCollection : { total: 0, items: [] };

  return news;
};

// Get News
const GetPortfolios = async () => {
  const portfolioQuery = `{
    portfoliosCollection {
      items {
        slug
      }
    }
  }`;

  const response = await callContentful(portfolioQuery);

  const portfolios = response.data.portfoliosCollection ? response.data.portfoliosCollection : { total: 0, items: [] };

  return portfolios;
};

generateSiteMap();
