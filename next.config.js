/** @type {import('next').NextConfig} */
const nextConfig = {
  // webpack: (config, { isServer }) => {
  //   if (isServer) {
  //     require("./utils/sitemap.js");
  //   }
  //   return config;
  // },
  reactStrictMode: true,
  swcMinify: true,
  images: {
    loader: "akamai",
    path: "",
  },
  basePath: "",
  assetPrefix: "",
  trailingSlash: true,
  output: 'standalone',
  compress: false,
  headers: async () => {
    return [
      {
        source: "/assets/(.*)",
        headers: [
          {
            key: 'cache-control',
            value: 'public, max-age=31536000',
          }
        ],
      },
    ];
  }
};

module.exports = nextConfig;
