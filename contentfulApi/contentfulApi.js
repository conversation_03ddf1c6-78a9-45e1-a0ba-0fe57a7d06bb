async function callContentful(query, isPreviewMode = false) {
  const fetchUrl = `https://graphql.contentful.com/content/v1/spaces/${process.env.CONTENTFUL_SPACE_ID}/environments/${process.env.CONTENTFUL_ENVIRONMENT}`;

  const fetchOptions = {
    method: "POST",
    headers: {
      Authorization: `Bearer ${isPreviewMode ? process.env.CONTENTFUL_PREVIEW_TOKEN : process.env.CONTENTFUL_ACCESS_TOKEN}`,
      "Content-Type": "application/json",
    },
    body: JSON.stringify({ query }),
  };

  try {
    let data = await fetch(fetchUrl, fetchOptions).then((response) => response.json());
    const collectionKey = Object.keys(data.data)[0];
    data = { data: { [collectionKey]: { items: data?.data[collectionKey]?.items?.filter((item) => item != null) || [] } } };
    return data;
  } catch (error) {
    console.log(error);
    throw new Error("Could not fetch data from Contentful!");
  }
}

// Get Home Page Hero Images
async function GetHomePageHeroImages(isPreviewMode = false) {
  const query = `{
      homeHeroImagesCollection(preview: ${isPreviewMode}) {
        items {
          title
          description
          banner {
            url
          }
          link
          linkText
        }
      }
    }`;

  const response = await this.callContentful(query, isPreviewMode);

  const homeHeroImages = response.data.homeHeroImagesCollection ? response.data.homeHeroImagesCollection : { total: 0, items: [] };

  return homeHeroImages;
}

// Get Home Page Why URW Section
async function GetHomePageWhyUrwSection(isPreviewMode = false) {
  const query = `{
      homeWhyUrwCollection(preview: ${isPreviewMode}) {
        items {
          title
          description
          icon {
            url
          }
        }
      }
    }`;

  const response = await this.callContentful(query, isPreviewMode);

  const homeWhyUrw = response.data.homeWhyUrwCollection ? response.data.homeWhyUrwCollection : { total: 0, items: [] };

  return homeWhyUrw;
}

// Get Portfolio Page Additional Experience
async function GetHomePortfolioPageAdditionalExperience(isPreviewMode = false) {
  const query = `{
      portfolioAdditionalExperienceCollection(preview:${isPreviewMode}) {
        items {
          title
          logosCollection(limit: 10) {
            ... on PortfolioAdditionalExperienceLogosCollection {
              items {
                title
                subTitle
                logo {
                  url
                }
              }
            }
          }
        }
      }
    }`;

  const response = await this.callContentful(query, isPreviewMode);

  const portfolioAdditionalExperience = response.data.portfolioAdditionalExperienceCollection ? response.data.portfolioAdditionalExperienceCollection : { total: 0, items: [] };

  return portfolioAdditionalExperience;
}

// Get Home Page Cards
async function GetHomePageCards(isPreviewMode = false) {
  const query = `{
      homeCardsCollection(preview: ${isPreviewMode}) {
        items {
          title
          subTitle
          cardsCollection(limit: 15) {
            ... on HomeCardsCardsCollection {
              items {
                ... on Portfolios {
                  title
                  subTitle
                  location
                  slug
                  heroImage {
                    url
                  }
                  logo {
                    url
                  }
                }
              }
            }
          }
        }
      }
    }`;

  const response = await this.callContentful(query, isPreviewMode);

  const homeCards = response.data.homeCardsCollection ? response.data.homeCardsCollection : { total: 0, items: [] };

  return homeCards;
}

// Get Portfolio Page Cards
async function GetPortFolioPageCards(isPreviewMode = false) {
  const query = `{
      portfolioCardsCollection(preview: ${isPreviewMode}) {
        items {
          title
          subTitle
          cardsCollection(limit: 15) {
            ... on PortfolioCardsCardsCollection {
              items {
                ... on Portfolios {
                  title
                  subTitle
                  location
                  slug
                  heroImage {
                    url
                  }
                  logo {
                    url
                  }
                  factSheet{
                    url
                  }
                }
              }
            }
          }
        }
      }
    }`;

  const response = await this.callContentful(query, isPreviewMode);

  const portfolioCards = response.data.portfolioCardsCollection ? response.data.portfolioCardsCollection : { total: 0, items: [] };

  return portfolioCards;
}

// Get Home Page Highlights
async function GetHomePageHighlights(isPreviewMode = false) {
  const query = `{
      homeHighlightsCollection(preview: ${isPreviewMode}) {
        items {
          title
          highlightsCollection {
            ... on HomeHighlightsHighlightsCollection {
              items {
                ... on Highlights {
                  title
                  stats
                }
              }
            }
          }
        }
      }
    }`;

  const response = await this.callContentful(query, isPreviewMode);

  const homeHighlights = response.data.homeHighlightsCollection ? response.data.homeHighlightsCollection : { total: 0, items: [] };

  return homeHighlights;
}
// Get Home Page Awards And Honors
async function GetHomePageAwardsAndHonors(isPreviewMode = false) {
  const query = `{
      homeAwardsAndHonorsCollection(preview: ${isPreviewMode}) {
        items {
          title
          logosCollection(limit: 10) {
            ... on HomeAwardsAndHonorsLogosCollection {
              items {
                title
                subTitle
                logo {
                  url
                }
              }
            }
          }
        }
      }
    }`;

  const response = await this.callContentful(query, isPreviewMode);

  const homeAwardsAndHonors = response.data.homeAwardsAndHonorsCollection ? response.data.homeAwardsAndHonorsCollection : { total: 0, items: [] };

  return homeAwardsAndHonors;
}

// Get Latest News And Stories
async function GetLatestNewsAndStories(isPreviewMode = false) {
  const query = `{
      newsCollection(preview: ${isPreviewMode}, order: newsPublishedDate_DESC, limit: 5,where: {tags_not_in: ["Terminal One","Events"]}) {
        items {
          title
          subTitle
          tags
          slug
          bannerImage {
            url
          }
        }
      }
    }`;

  const response = await this.callContentful(query, isPreviewMode);

  const latestNews = response.data.newsCollection ? response.data.newsCollection : { total: 0, items: [] };

  return latestNews;
}

// Get News Details By Slug
async function GetNewsDetailsBySlug(newsDetailSlug, isPreviewMode = false) {
  const query = `{
      newsCollection(
        where: {slug: "${newsDetailSlug}"}
        preview: ${isPreviewMode},
        limit: 1,
      ) {
        items {
          sys{
            firstPublishedAt
          }
          newsPublishedDate
          title
          subTitle
          bannerImage {
            url
          }
          slug
          tags
          quoteText
          quoteImage {
            url
          }
          contentBlock {
            json
            links {
              assets {
                block {
                  title
                  fileName
                  sys {
                    id
                  }
                  url
                }
              }
            }
          }
          contentBlock2{
            json
            links {
              assets {
                block {
                  title
                  fileName
                  sys {
                    id
                  }
                  url
                }
              }
            }
          }
          contentBlock3{
            json
            links {
              assets {
                block {
                  title
                  fileName
                  sys {
                    id
                  }
                  url
                }
              }
            }
          }
          metaTitle
          metaRobots
          metaKeywords
          metaDescription
          carouselCollection(limit: 5) {
            items {
              title
              carouselImage {
                url
              }
            }
          }
          relatedNewsCollection(limit: 5) {
            items {
              title
              bannerImage {
                url
              }
              tags
              title
              subTitle
              slug
            }
          }
        }
      }
    }`;

  const response = await this.callContentful(query, isPreviewMode);

  const newsDetails = response.data.newsCollection ? response.data.newsCollection : { total: 0, items: [] };

  return newsDetails;
}

// Get Latest News And Stories
async function GetAllNews(isPreviewMode = false) {
  const query = `{
      newsCollection(
        where: {tags_not_in: ["Terminal One","Events"]}
        preview: false
        order: newsPublishedDate_DESC
        limit: 500
      ) {
        items {
          sys {
            firstPublishedAt
          }
          newsPublishedDate
          title
          subTitle
          slug
          airport
          tags
          slug
          bannerImage {
            url
          }
        }
      }
    }`;

  const response = await this.callContentful(query, isPreviewMode);

  const allNews = response.data.newsCollection ? response.data.newsCollection : { total: 0, items: [] };

  return allNews;
}

// Get Latest News And Stories for Terminal One
async function GetTerminalOneNews(isPreviewMode = false) {
  const query = `{
      newsCollection(where: { tags_in: ["Terminal One"]}, preview: ${isPreviewMode}, order: newsPublishedDate_DESC,limit: 500) {
        items {
          sys {
            firstPublishedAt
          }
          newsPublishedDate
          title
          subTitle
          slug
          airport
          tags
          slug
          bannerImage {
            url
          }
        }
      }
    }`;

  const response = await this.callContentful(query, isPreviewMode);

  const allNews = response.data.newsCollection ? response.data.newsCollection : { total: 0, items: [] };

  return allNews;
}

async function GetEventNews(isPreviewMode = false) {
  const query = `{
      newsCollection(preview: ${isPreviewMode}, order: newsPublishedDate_DESC,limit: 500, where: {tags: "Events"}) {
        items {
          sys {
            firstPublishedAt
            id
          }
          newsPublishedDate
          title
          subTitle
          slug
          airport
          tags
          slug
          outreachVideo
          bannerImage {
            url
          }
        }
      }
    }`;

  const response = await this.callContentful(query, isPreviewMode);

  const allNews = response.data.newsCollection ? response.data.newsCollection : { total: 0, items: [] };

  return allNews;
}

// Get Latest News And Stories By Category
async function GetAllNewsByCategory(category, isPreviewMode = false) {
  const query = `{
      newsCollection(preview: ${isPreviewMode}, order: newsPublishedDate_DESC,limit: 500,where: {airport: "${category}"}) {
        items {
          sys {
            firstPublishedAt
          }
          newsPublishedDate
          title
          subTitle
          slug
          airport
          tags
          slug
          bannerImage {
            url
          }
        }
      }
    }`;

  const response = await this.callContentful(query, isPreviewMode);

  const allNews = response.data.newsCollection ? response.data.newsCollection : { total: 0, items: [] };

  return allNews;
}

// Get Advance Network News
async function GetNewsByTags(tag, isPreviewMode = false) {
  const query = `{
      newsCollection(preview: ${isPreviewMode}, order: newsPublishedDate_DESC,limit: 500,where: {tags: "${tag}"}) {
        items {
          sys {
            firstPublishedAt
          }
          newsPublishedDate
          title
          subTitle
          slug
          airport
          tags
          slug
          bannerImage {
            url
          }
        }
      }
    }`;

  const response = await this.callContentful(query, isPreviewMode);

  const allNews = response.data.newsCollection ? response.data.newsCollection : { total: 0, items: [] };

  return allNews;
}

// Get Portfolio Page Hero Image
async function GetPortfolioPageHeroImage(isPreviewMode = false) {
  const query = `{
      portfolioHeroImageCollection(preview: ${isPreviewMode}) {
        items {
          title
          description
          bannerImage {
            url
          }
        }
      }
    }`;

  const response = await this.callContentful(query, isPreviewMode);

  const portfolioHero = response.data.portfolioHeroImageCollection ? response.data.portfolioHeroImageCollection : { total: 0, items: [] };

  return portfolioHero;
}

// Get Portfolio Detail By Slug
async function GetPortfolioDetailsBySlug(portfolioDetailSlug, isPreviewMode = false) {
  const query = `{
      portfoliosCollection(where: {slug: "${portfolioDetailSlug}"}, limit: 1, preview: ${isPreviewMode}) {
        items {
          title
          subTitle
          metaTitle
          metaDescription
          slug
          location
          heroImage {
            url
          }
          logo {
            url
          }
          factSheet{
            url
          }
          topSectionTitle
          topSectionSubTitle
          topSectionDescription {
            json
          }
          topSectionLogo {
            url
          }
          portfolioHighlightsCollection(limit: 10) {
            items {
              title
              stats
            }
          }
          highlightNote
          portfolioDescription {
            json
          }
          factSheetText
          portfolioTabsCollection(limit: 10) {
            items {
              tabTitle
              tabImage {
                url
              }
              title
              description
              content {
                json
              }
              logo {
                url
              }
              logoTitle
              logoSubTitle
              highlightsCollection(limit: 10) {
                items {
                  title
                  stats
                }
              }
            }
          }
          newsTitle
          newsCollection(limit: 10) {
            items {
              title
              subTitle
              slug
              tags
              bannerImage {
                url
              }
            }
          }
          letsDoBusiness {
            title
            description
            image {
              url
            }
            link
            linkText
          }
        }
      }
    }`;

  const response = await this.callContentful(query, isPreviewMode);

  const portfolioDetails = response.data.portfoliosCollection ? response.data.portfoliosCollection : { total: 0, items: [] };

  return portfolioDetails;
}

// Get All Portfolio Slugs
async function GetAllPortfolioSlugs(isPreviewMode = false) {
  const query = `{
      portfoliosCollection(preview: ${isPreviewMode}) {
        items {
          slug
        }
      }
    }`;

  const response = await this.callContentful(query, isPreviewMode);

  const slugs = response.data.portfoliosCollection ? response.data.portfoliosCollection : { total: 0, items: [] };

  return slugs;
}

// Get Leasing Page Hero Image
async function GetLeasingPageHeroImage(isPreviewMode = false) {
  const query = `{
      leasingHeroImageCollection(preview: ${isPreviewMode}) {
        items {
          title
          description
          bannerImage {
            url
          }
        }
      }
    }`;

  const response = await this.callContentful(query, isPreviewMode);

  const leasingHeroImage = response.data.leasingHeroImageCollection ? response.data.leasingHeroImageCollection : { total: 0, items: [] };

  return leasingHeroImage;
}

// Get Leasing Page Carousel
async function GetLeasingPageCarousel(isPreviewMode = false) {
  const query = `{
      leasingCarouselCollection(preview: ${isPreviewMode}) {
        items {
          title
          imageCollection {
            items {
              description
              url
            }
          }
        }
      }
    }`;

  const response = await this.callContentful(query, isPreviewMode);

  const leasingCarousel = response.data.leasingCarouselCollection ? response.data.leasingCarouselCollection : { total: 0, items: [] };

  return leasingCarousel;
}

// Get About Page Carousel
async function GetAboutPageCarousel(isPreviewMode = false) {
  const query = `{
      portfoliosCollection(preview: ${isPreviewMode}) {
        items {
          carouselImage {
            description
            url
          }
        }
      }
    }`;

  const response = await this.callContentful(query, isPreviewMode);

  const portfolios = response.data.portfoliosCollection ? response.data.portfoliosCollection : { total: 0, items: [] };

  return portfolios;
}
// Get Leasing Page Featured Brands
async function GetLeasingPageFeaturedBrands(isPreviewMode = false) {
  const query = `{
      leasingFeaturedBrandsCollection(preview: ${isPreviewMode}) {
        items {
          title
          title2
          logosCollection(limit: 50) {
            items {
              logo {
                url
              }
            }
          }
        }
      }
    }`;

  const response = await this.callContentful(query, isPreviewMode);

  const leasingFeaturedBrands = response.data.leasingFeaturedBrandsCollection ? response.data.leasingFeaturedBrandsCollection : { total: 0, items: [] };

  return leasingFeaturedBrands;
}

// Get Leasing Page Section
async function GetLeasingPageSection(isPreviewMode = false) {
  const query = `{
      leasingSectionCollection(preview:${isPreviewMode}) {
        items {
          title
          description {
            json
          }
          link
          linkText
        }
      }
    }`;

  const response = await this.callContentful(query, isPreviewMode);

  const leasingSection = response.data.leasingSectionCollection ? response.data.leasingSectionCollection : { total: 0, items: [] };

  return leasingSection;
}

// Get About Page Section
async function GetAboutPageSection(isPreviewMode = false) {
  const query = `{
      aboutSectionCollection(preview:${isPreviewMode}) {
        items {
          title
          title2
          section2Title
          section2SubTitle
          section2Description {
            json
          }
          section3Title
          section3Description {
            json
          }
          section3Logo {
            url
          }
          section3Link
          section3LinkText
          section3ImagesCollection(limit: 5) {
            items {
              logo {
                url
              }
              description {
                json
              }
            }
          }
          description {
            json
          }
          link
          linkText
        }
      }
    }`;

  const response = await this.callContentful(query, isPreviewMode);

  const aboutSection = response.data.aboutSectionCollection ? response.data.aboutSectionCollection : { total: 0, items: [] };

  return aboutSection;
}

// Get Leasing Page Card with Video Or Image
async function GetLeasingPageCardWithVideoOrImage(isPreviewMode = false) {
  const query = `{
      leasingCardWithImageOrVideoCollection(preview:${isPreviewMode}) {
        items {
          title {
            json
          }
          description
          card {
            ... on CardWithVideo {
              title
              content {
                json
              }
              video {
                url
              }
              videoLink
              linkText
            }
          }
        }
      }
    }`;

  const response = await this.callContentful(query, isPreviewMode);

  const leasingCardWithImageOrVideo = response.data.leasingCardWithImageOrVideoCollection ? response.data.leasingCardWithImageOrVideoCollection : { total: 0, items: [] };

  return leasingCardWithImageOrVideo;
}

// Get News Page Hero Image
async function GetNewsPageHeroImage(isPreviewMode = false) {
  const query = `{
      newsHeroImageCollection(preview: ${isPreviewMode}) {
        items {
          title
          description
          bannerImage {
            url
          }
        }
      }
    }`;

  const response = await this.callContentful(query, isPreviewMode);

  const newsHeroImage = response.data.newsHeroImageCollection ? response.data.newsHeroImageCollection : { total: 0, items: [] };

  return newsHeroImage;
}

// Get About Page Hero Image
async function GetAboutPageHeroImage(isPreviewMode = false) {
  const query = `{
      aboutUsHeroImageCollection(preview: ${isPreviewMode}) {
        items {
          title
          description
          bannerImage {
            url
          }
        }
      }
    }`;

  const response = await this.callContentful(query, isPreviewMode);

  const aboutHeroImage = response.data.aboutUsHeroImageCollection ? response.data.aboutUsHeroImageCollection : { total: 0, items: [] };

  return aboutHeroImage;
}

// Get Community / Sustainability Page Hero Image
async function GetCommunityPageHeroImage(isSustainabilityPage = false, isPreviewMode = false) {
  const query = `{
      communityHeroImageCollection(where : {isSustainabilityPage :${isSustainabilityPage}}, preview: ${isPreviewMode}) {
        items {
          title
          description
          bannerImage {
            url
          }
        }
      }
    }`;

  const response = await this.callContentful(query, isPreviewMode);

  const communityHeroImage = response.data.communityHeroImageCollection ? response.data.communityHeroImageCollection : { total: 0, items: [] };

  return communityHeroImage;
}

// Get Contact Page Hero Image
async function GetContactPageHeroImage(isPreviewMode = false) {
  const query = `{
      contactHeroImageCollection(preview: ${isPreviewMode}) {
        items {
          title
          description
          bannerImage {
            url
          }
        }
      }
    }`;

  const response = await this.callContentful(query, isPreviewMode);

  const contactHeroImage = response.data.contactHeroImageCollection ? response.data.contactHeroImageCollection : { total: 0, items: [] };

  return contactHeroImage;
}

// Get Contact Page Banner
async function GetContactPageBanner(isPreviewMode = false) {
  const query = `{
      contactBannerCollection(limit: 1, preview: ${isPreviewMode}) {
        items {
          title
          bannerImage {
            url
          }
          link
        }
      }
    }`;

  const response = await this.callContentful(query, isPreviewMode);

  const contactBanner = response.data.contactBannerCollection ? response.data.contactBannerCollection : { total: 0, items: [] };

  return contactBanner;
}

// Get Contact Page SEO Meta
async function GetContactPageSEOMeta(isPreviewMode = false) {
  const query = `{
      seoMetaContactCollection(preview: ${isPreviewMode}) {
        items {
          title
          description
          keywords
          robots
        }
      }
    }`;

  const response = await this.callContentful(query, isPreviewMode);

  const seoMetaContact = response.data.seoMetaContactCollection ? response.data.seoMetaContactCollection : { total: 0, items: [] };

  return seoMetaContact;
}

// Get Community Page SEO Meta
async function GetCommunityPageSEOMeta(isSustainabilityPage = false, isPreviewMode = false) {
  const query = `{
      seoMetaCommunityCollection(where : {isSustainabilityPage :${isSustainabilityPage}}, preview: ${isPreviewMode}) {
        items {
          title
          description
          keywords
          robots
        }
      }
    }`;

  const response = await this.callContentful(query, isPreviewMode);

  const seoMetaCommunity = response.data.seoMetaCommunityCollection ? response.data.seoMetaCommunityCollection : { total: 0, items: [] };

  return seoMetaCommunity;
}

// Get About Page SEO Meta
async function GetAboutPageSEOMeta(isPreviewMode = false) {
  const query = `{
        seoMetaAboutUsCollection(preview: ${isPreviewMode}) {
          items {
            title
            description
            keywords
            robots
          }
        }
      }`;

  const response = await this.callContentful(query, isPreviewMode);

  const seoMetaAbout = response.data.seoMetaAboutUsCollection ? response.data.seoMetaAboutUsCollection : { total: 0, items: [] };

  return seoMetaAbout;
}

// Get Home Page SEO Meta
async function GetHomePageSEOMeta(isPreviewMode = false) {
  const query = `{
      seoMetaHomePageCollection(preview: ${isPreviewMode}) {
        items {
          title
          description
          keywords
          robots
        }
      }
    }`;

  const response = await this.callContentful(query, isPreviewMode);

  const seoMetaHomePage = response.data.seoMetaHomePageCollection ? response.data.seoMetaHomePageCollection : { total: 0, items: [] };

  return seoMetaHomePage;
}

// Get Leasing Page SEO Meta
async function GetLeasingPageSEOMeta(isPreviewMode = false) {
  const query = `{
      seoMetaLeasingPageCollection(preview: ${isPreviewMode}) {
        items {
          title
          description
          keywords
          robots
        }
      }
    }`;

  const response = await this.callContentful(query, isPreviewMode);

  const seoMetaLeasingPage = response.data.seoMetaLeasingPageCollection ? response.data.seoMetaLeasingPageCollection : { total: 0, items: [] };

  return seoMetaLeasingPage;
}

// Get Leasing Page Banner
async function GetLeasingPageBanner(isPreviewMode = false) {
  const query = `{
      leasingBannerCollection(preview:${isPreviewMode}) {
        items {
          title {
            json
          }
          bannerImage {
            url
          }
          link
          linkText
        }
      }
    }`;

  const response = await this.callContentful(query, isPreviewMode);

  const leasingBanner = response.data.leasingBannerCollection ? response.data.leasingBannerCollection : { total: 0, items: [] };

  return leasingBanner;
}
// Get News Page SEO Meta
async function GetNewsPageSEOMeta(isPreviewMode = false) {
  const query = `{
      seoMetaNewsCollection(preview: ${isPreviewMode}) {
        items {
          title
          description
          keywords
          robots
        }
      }
    }`;

  const response = await this.callContentful(query, isPreviewMode);

  const seoMetaNews = response.data.seoMetaNewsCollection ? response.data.seoMetaNewsCollection : { total: 0, items: [] };

  return seoMetaNews;
}

// Get Portfolio Page SEO Meta
async function GetPortfolioPageSEOMeta(isPreviewMode = false) {
  const query = `{
      seoMetaPortfolioPageCollection(preview: ${isPreviewMode}) {
        items {
          title
          description
          keywords
          robots
        }
      }
    }`;

  const response = await this.callContentful(query, isPreviewMode);

  const seoMetaPortfolio = response.data.seoMetaPortfolioPageCollection ? response.data.seoMetaPortfolioPageCollection : { total: 0, items: [] };

  return seoMetaPortfolio;
}

// Get Team Page SEO Meta
async function GetTeamPageSEOMeta(isPreviewMode = false) {
  const query = `{
      seoMetaTeamCollection(preview: ${isPreviewMode}) {
        items {
          title
          description
          keywords
          robots
        }
      }
    }`;

  const response = await this.callContentful(query, isPreviewMode);

  const seoMetaTeam = response.data.seoMetaTeamCollection ? response.data.seoMetaTeamCollection : { total: 0, items: [] };

  return seoMetaTeam;
}

// Get Home Page > Lets Do Business
async function GetHomePageLetsDoBusiness(isPreviewMode = false) {
  const query = `{
      homeLetsDoBusinessCollection(preview: ${isPreviewMode}) {
        items {
          card {
            title
            description
            image {
              url
            }
            link
            linkText
          }
        }
      }
    }`;

  const response = await this.callContentful(query, isPreviewMode);

  const homeLetsDoBusiness = response.data.homeLetsDoBusinessCollection ? response.data.homeLetsDoBusinessCollection : { total: 0, items: [] };

  return homeLetsDoBusiness;
}

// Get Portfolio Page > Lets Do Business
async function GetPortfolioPageLetsDoBusiness(isPreviewMode = false) {
  const query = `{
      portfolioLetsDoBusinessCollection(preview: ${isPreviewMode}) {
        items {
          card {
            title
            description
            image {
              url
            }
            link
            linkText
          }
        }
      }
    }`;

  const response = await this.callContentful(query, isPreviewMode);

  const portfolioLetsDoBusiness = response.data.portfolioLetsDoBusinessCollection ? response.data.portfolioLetsDoBusinessCollection : { total: 0, items: [] };

  return portfolioLetsDoBusiness;
}

// Get Portfolio Details Page > Lets Do Business
async function GetPortfolioDetailsPageLetsDoBusiness(isPreviewMode = false) {
  const query = `{
      portfolioDetailsLetsDoBusinessCollection(preview: ${isPreviewMode}) {
        items {
          card {
            title
            description
            image {
              url
            }
            link
            linkText
          }
        }
      }
    }`;

  const response = await this.callContentful(query, isPreviewMode);

  const portfolioDetailsLetsDoBusiness = response.data.portfolioDetailsLetsDoBusinessCollection ? response.data.portfolioDetailsLetsDoBusinessCollection : { total: 0, items: [] };

  return portfolioDetailsLetsDoBusiness;
}

// Get Leasing Page > Lets Do Business
async function GetLeasingPageLetsDoBusiness(isPreviewMode = false) {
  const query = `{
      leasingLetsDoBusinessCollection(preview: ${isPreviewMode}) {
        items {
          card {
            title
            description
            image {
              url
            }
            link
            linkText
          }
        }
      }
    }`;

  const response = await this.callContentful(query, isPreviewMode);

  const leasingLetsDoBusiness = response.data.leasingLetsDoBusinessCollection ? response.data.leasingLetsDoBusinessCollection : { total: 0, items: [] };

  return leasingLetsDoBusiness;
}

// Get News Page > Lets Do Business
async function GetNewsPageLetsDoBusiness(isPreviewMode = false) {
  const query = `{
      newsLetsDoBusinessCollection(preview: ${isPreviewMode}) {
        items {
          card {
            title
            description
            image {
              url
            }
            link
            linkText
          }
        }
      }
    }`;

  const response = await this.callContentful(query, isPreviewMode);

  const newsLetsDoBusiness = response.data.newsLetsDoBusinessCollection ? response.data.newsLetsDoBusinessCollection : { total: 0, items: [] };

  return newsLetsDoBusiness;
}

// Get News Detail Page > Lets Do Business
async function GetNewsDetailPageLetsDoBusiness(isPreviewMode = false) {
  const query = `{
      newsDetailLetsDoBusinessCollection(preview: ${isPreviewMode}) {
        items {
          card {
            title
            description
            image {
              url
            }
            link
            linkText
          }
        }
      }
    }`;

  const response = await this.callContentful(query, isPreviewMode);

  const newsDetailLetsDoBusiness = response.data.newsDetailLetsDoBusinessCollection ? response.data.newsDetailLetsDoBusinessCollection : { total: 0, items: [] };

  return newsDetailLetsDoBusiness;
}

// Get Community Page > Lets Do Business
async function GetCommunityPageLetsDoBusiness(isSustainabilityPage = false, isPreviewMode = false) {
  const query = `{
      communityLetsDoBusinessCollection(where : {isSustainabilityPage :${isSustainabilityPage}}, preview: ${isPreviewMode}) {
        items {
          card {
            title
            description
            image {
              url
            }
            link
            linkText
          }
        }
      }
    }`;

  const response = await this.callContentful(query, isPreviewMode);

  const communityLetsDoBusiness = response.data.communityLetsDoBusinessCollection ? response.data.communityLetsDoBusinessCollection : { total: 0, items: [] };

  return communityLetsDoBusiness;
}

// Get About Page > Lets Do Business
async function GetAboutPageLetsDoBusiness(isPreviewMode = false) {
  const query = `{
      aboutUsLetsDoBusinessCollection(preview: ${isPreviewMode}) {
        items {
          card {
            title
            description
            image {
              url
            }
            link
            linkText
          }
        }
      }
    }`;

  const response = await this.callContentful(query, isPreviewMode);

  const aboutLetsDoBusiness = response.data.aboutUsLetsDoBusinessCollection ? response.data.aboutUsLetsDoBusinessCollection : { total: 0, items: [] };

  return aboutLetsDoBusiness;
}
// Get Community Page > Partners
async function GetCommunityPagePartners(isPreviewMode = false) {
  const query = `{
      communityPartnersCollection(preview: ${isPreviewMode}) {
        items {
          title
          logosCollection(limit: 10) {
            items {
              logo {
                url
              }
            }
          }
        }
      }
    }`;

  const response = await this.callContentful(query, isPreviewMode);

  const communityPartners = response.data.communityPartnersCollection ? response.data.communityPartnersCollection : { total: 0, items: [] };

  return communityPartners;
}

// Get Community Page > Card With Image
async function GetCommunityPageCardWithImage(isPreviewMode = false) {
  const query = `{
      communityCardWithImageOrVideoCollection(preview:${isPreviewMode}) {
        items {
          ... on CommunityCardWithImageOrVideo {
            card {
              ... on CardWithImage {
                title
                content {
                  json
                }
                image {
                  url
                }
              }
            }
          }
        }
      }
    }`;

  const response = await this.callContentful(query, isPreviewMode);

  const communityCardWithImage = response.data.communityCardWithImageOrVideoCollection ? response.data.communityCardWithImageOrVideoCollection : { total: 0, items: [] };

  return communityCardWithImage;
}

// Get Community Page > Card With Video
async function GetCommunityPageCardWithVideo(isSustainabilityPage = false, isPreviewMode = false) {
  const query = `{
    communityCardWithImageOrVideoCollection(where : {isSustainabilityPage :${isSustainabilityPage}},preview:${isPreviewMode}) {
      items {
        ... on CommunityCardWithImageOrVideo {
          card {
            ... on CardWithVideo {
              title
              content {
                json
              }
              videoLink
              link
              linkText
            }
          }
        }
      }
    }
  }`;

  const response = await this.callContentful(query, isPreviewMode);

  const communityCardWithVideo = response.data.communityCardWithImageOrVideoCollection ? response.data.communityCardWithImageOrVideoCollection : { total: 0, items: [] };

  return communityCardWithVideo;
}

// Get Community Page > Where We Focus Section
async function GetCommunityPageWhereWeFocus(isSustainabilityPage = false, isPreviewMode = false) {
  const query = `{
      communityWhereWeFocusCollection(where : {isSustainabilityPage :${isSustainabilityPage}}, preview:${isPreviewMode},order:sys_firstPublishedAt_ASC) {
        items {
          title
          description
          image {
            url
          }
          link
          linkText
        }
      }
    }`;

  const response = await this.callContentful(query, isPreviewMode);

  const communityWhereWeFocus = response.data.communityWhereWeFocusCollection ? response.data.communityWhereWeFocusCollection : { total: 0, items: [] };

  return communityWhereWeFocus;
}

// Get Contact Page > Lets Do Business
async function GetContactPageLetsDoBusiness(isPreviewMode = false) {
  const query = `{
      contactUsLetDoBusinessCollection(preview: ${isPreviewMode}) {
        items {
          card {
            title
            description
            image {
              url
            }
            link
            linkText
          }
        }
      }
    }`;

  const response = await this.callContentful(query, isPreviewMode);

  const contactUsLetDoBusiness = response.data.contactUsLetDoBusinessCollection ? response.data.contactUsLetDoBusinessCollection : { total: 0, items: [] };

  return contactUsLetDoBusiness;
}

// Get Portfolio Page > Card With Video Or Image
async function GetPortfolioPageWithVideoOrImage(isPreviewMode = false) {
  const query = `{
      portfolioCardWithImageOrVideoCollection(preview: ${isPreviewMode}) {
        items {
          ... on PortfolioCardWithImageOrVideo {
            card {
              ... on CardWithImage {
                title
                content {
                  json
                }
                link
                linkText
                image {
                  url
                }
              }
              ... on CardWithVideo {
                title
                content {
                  json
                }
                video {
                  url
                }
                videoLink
                link
                linkText
              }
            }
          }
        }
      }
    }`;

  const response = await this.callContentful(query, isPreviewMode);

  const portfolioCardWithImageOrVideo = response.data.portfolioCardWithImageOrVideoCollection ? response.data.portfolioCardWithImageOrVideoCollection : { total: 0, items: [] };

  return portfolioCardWithImageOrVideo;
}

// Get Portfolio Footer Links
async function GetPortfolioFooterLinks(isPreviewMode = false) {
  const query = `{
      portfoliosCollection(order: sys_publishedAt_ASC, preview:${isPreviewMode}) {
        items {
          footerText
          slug
        }
      }
    }`;

  const response = await this.callContentful(query, isPreviewMode);

  const portfolios = response.data.portfoliosCollection ? response.data.portfoliosCollection : { total: 0, items: [] };

  return portfolios;
}

// Get Leasing Page > Card With Video Or Image
async function GetLeasingPageWithVideoOrImage(isPreviewMode = false) {
  const query = `{
      leasingCardWithImageOrVideoCollection(preview: ${isPreviewMode}) {
        items {
          name
          ... on LeasingCardWithImageOrVideo {
            card {
              ... on CardWithImage {
                title
                content {
                  json
                }
                link
                linkText
                image {
                  url
                }
              }
              ... on CardWithVideo {
                title
                content {
                  json
                }
                video {
                  url
                }
                videoLink
                link
                linkText
              }
            }
          }
        }
      }
    }`;

  const response = await this.callContentful(query, isPreviewMode);

  const leasingCardWithImageOrVideo = response.data.leasingCardWithImageOrVideoCollection ? response.data.leasingCardWithImageOrVideoCollection : { total: 0, items: [] };

  return leasingCardWithImageOrVideo;
}

// Get Community Page > Card With Video Or Image
async function GetCommunityPageWithVideoOrImage(isPreviewMode = false) {
  const query = `{
      communityCardWithImageOrVideoCollection(preview: ${isPreviewMode}) {
        items {
          name
          ... on CommunityCardWithImageOrVideo {
            card {
              ... on CardWithImage {
                title
                content {
                  json
                }
                link
                linkText
                image {
                  url
                }
              }
              ... on CardWithVideo {
                title
                content {
                  json
                }
                video {
                  url
                }
                videoLink
                link
                linkText
              }
            }
          }
        }
      }
    }`;

  const response = await this.callContentful(query, isPreviewMode);

  const communityCardWithImageOrVideo = response.data.communityCardWithImageOrVideoCollection ? response.data.communityCardWithImageOrVideoCollection : { total: 0, items: [] };

  return communityCardWithImageOrVideo;
}

// Get Supplier Diversity Page
async function GetSupplierDiversityPage(isPreviewMode = false) {
  const query = `{
        supplierDiversityCollection(preview: ${isPreviewMode}) {
          items {
            banner {
              title
              url
            }
            sectionTitle1
            sectionTitle2
            section {
              json
            }
            seoTitle
            seoDescription
          }
        }
      }`;

  const response = await this.callContentful(query, isPreviewMode);

  const supplierDiversityCollection = response.data.supplierDiversityCollection ? response.data.supplierDiversityCollection : { total: 0, items: [] };

  return supplierDiversityCollection;
}


// Export the functions
export default {
  callContentful,
  GetHomePageHeroImages,
  GetHomePageWhyUrwSection,
  GetHomePortfolioPageAdditionalExperience,
  GetHomePageCards,
  GetPortFolioPageCards,
  GetHomePageHighlights,
  GetHomePageAwardsAndHonors,
  GetLatestNewsAndStories,
  GetNewsDetailsBySlug,
  GetAllNews,
  GetTerminalOneNews,
  GetEventNews,
  GetAllNewsByCategory,
  GetNewsByTags,
  GetPortfolioPageHeroImage,
  GetPortfolioDetailsBySlug,
  GetAllPortfolioSlugs,
  GetLeasingPageHeroImage,
  GetLeasingPageCarousel,
  GetAboutPageCarousel,
  GetLeasingPageFeaturedBrands,
  GetLeasingPageSection,
  GetAboutPageSection,
  GetLeasingPageCardWithVideoOrImage,
  GetNewsPageHeroImage,
  GetAboutPageHeroImage,
  GetCommunityPageHeroImage,
  GetContactPageHeroImage,
  GetContactPageBanner,
  GetContactPageLetsDoBusiness,
  GetPortfolioPageWithVideoOrImage,
  GetPortfolioFooterLinks,
  GetLeasingPageWithVideoOrImage,
  GetCommunityPageWithVideoOrImage,
  GetCommunityPagePartners,
  GetCommunityPageCardWithImage,
  GetCommunityPageCardWithVideo,
  GetCommunityPageWhereWeFocus,
  GetAboutPageLetsDoBusiness,
  GetNewsPageLetsDoBusiness,
  GetCommunityPageLetsDoBusiness,
  GetHomePageLetsDoBusiness,
  GetPortfolioPageLetsDoBusiness,
  GetLeasingPageLetsDoBusiness,
  GetHomePageSEOMeta,
  GetAboutPageSEOMeta,
  GetNewsPageSEOMeta,
  GetCommunityPageSEOMeta,
  GetContactPageSEOMeta,
  GetPortfolioPageSEOMeta,
  GetLeasingPageSEOMeta,
  GetTeamPageSEOMeta,
  GetLeasingPageBanner,
  GetPortfolioDetailsPageLetsDoBusiness,
  GetNewsDetailPageLetsDoBusiness,
  GetSupplierDiversityPage
};