name: Terraform-plan

on:
  pull_request:
    branches:
      - main
    paths:
      - "infrastructure/*"
      - ".github/*"
      - "Makefile"
      - ".terra*-version"

jobs:
  terraform-plan:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2

      - name: Set dev environment
        if: ${{ github.event.pull_request.base.ref == 'main' }}
        run: echo "_ENV=demo" >> $GITHUB_ENV

      - name: Configure Awscli
        run:  make awscli
        env:
          _AWS_ACCESS_KEY_ID: ${{ secrets[format('{0}_AWS_ACCESS_KEY_ID', env._ENV)] }}
          _AWS_SECRET_ACCESS_KEY: ${{ secrets[format('{0}_AWS_SECRET_ACCESS_KEY', env._ENV)] }}

      - name: Install tf and tg switch
        run: |
          rm -rf /usr/local/bin/terraform
          curl -L https://raw.githubusercontent.com/warrensbox/terraform-switcher/release/install.sh | sudo bash
          curl -L https://raw.githubusercontent.com/warrensbox/tgswitch/release/install.sh | sudo bash
          sudo chown -R $USER /usr/local/bin/

      - name: Setup Terraform plugin cache
        run:  make set-plugin-cache

      - name: Install and setup Terraform and Terragrunt version
        run:  make tf

      - name: Deployment Plan
        run:  make plan
