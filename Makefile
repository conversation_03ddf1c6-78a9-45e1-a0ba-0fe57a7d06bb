SHELL=/bin/bash
_ENV?=demo
_PROJECT=urw-airports
_AWS_PROFILE=$(_PROJECT)-$(_ENV)
_AWS_REGION?=us-west-2
GIT_PROJECT_NAME?=$(shell basename `git rev-parse --show-toplevel`)
GITHUB_SHA?=$(shell git rev-parse HEAD)
TERRAGRUNT_DIR=infrastructure/environments/$(_ENV)
SCRIPTS=infrastructure/scripts

.EXPORT_ALL_VARIABLES:
TF_env=$(_ENV)
TF_prefix=$(_PROJECT)-$(_ENV)
## Kept same as global but could be different for more repos/website
TF_resource_prefix=$(_PROJECT)-$(_ENV)
TF_project=$(_PROJECT)
TF_repo_name=$(GIT_PROJECT_NAME)
TF_aws_profile=$(_AWS_PROFILE)
TF_aws_region=$(_AWS_REGION)
TF_lambda_artifacts_bucket=$(TF_prefix)-lambda-artifacts
TF_lambda_source_code_file=$(_ENV)-$(TF_repo_name)-lambda-source-code.zip
TF_lambda_layer_node_modules_file=$(_ENV)-$(TF_repo_name)-lambda-node-modules.zip
TF_lambda_website_and_contentful_preview_source_code_file=$(_ENV)-$(TF_repo_name)-website-and-contentful-preview-lambda-source-code.zip
TF_ssm_parameter_path_global_prefix=/$(_PROJECT)/$(_ENV)
## Kept same as global but could be different for more repos/website
TF_ssm_parameter_path_repo_prefix=/$(_PROJECT)/$(_ENV)
.PHONY: build

sso:
	@aws sso login --profile $(_AWS_PROFILE)

## setup aws profile
awscli:
	@echo $(_AWS_PROFILE)
	@aws configure set aws_access_key_id $(_AWS_ACCESS_KEY_ID) --profile $(_AWS_PROFILE)
	@aws configure set aws_secret_access_key $(_AWS_SECRET_ACCESS_KEY) --profile $(_AWS_PROFILE)
	@aws configure set region $(_AWS_REGION) --profile $(_AWS_PROFILE)

## Deployment and Terraform/Terragrunt commands to run in all folders which has terragrunt.hcl file
init-upgrade:
	@cd $(TERRAGRUNT_DIR) && terragrunt run-all init -upgrade

target-init-upgrade:
	@cd $(TERRAGRUNT_DIR)/$(target) && terragrunt init -upgrade

init plan apply show destroy:
	@cd $(TERRAGRUNT_DIR) && terragrunt run-all $@

apply-ci:
	@cd $(TERRAGRUNT_DIR) && terragrunt run-all apply --terragrunt-non-interactive

output:
	@cd $(TERRAGRUNT_DIR) && terragrunt run-all output --json 2> /dev/null | jq -s add

providers-lock:
	@cd $(TERRAGRUNT_DIR) && terragrunt run-all providers lock \
		-platform=linux_amd64 \
		-platform=darwin_arm64

target-providers-lock:
	@cd $(TERRAGRUNT_DIR)/$(target) && terragrunt run-all providers lock \
		-platform=linux_amd64 \
		-platform=darwin_arm64

## run terraform commands in specific folder
# Use: make target-plan target=us-west-2/vpc
target-init:
	@cd $(TERRAGRUNT_DIR)/$(target) && terragrunt init

target-plan:
	@cd $(TERRAGRUNT_DIR)/$(target) && terragrunt plan

target-apply:
	@cd $(TERRAGRUNT_DIR)/$(target) && terragrunt apply

target-destroy:
	@cd $(TERRAGRUNT_DIR)/$(target) && terragrunt destroy

target-output:
	@cd $(TERRAGRUNT_DIR)/$(target) && terragrunt output --json 2> /dev/null

target-state:
	@cd $(TERRAGRUNT_DIR)/$(target) && terragrunt state list

target-refresh:
	@cd $(TERRAGRUNT_DIR)/$(target) && terragrunt refresh

## Remove state lock
# Use: make target-unlock target=us-west-2/vpc lock_id=d232-323-323
target-unlock:
	@cd $(TERRAGRUNT_DIR)/$(target) && echo yes | terragrunt force-unlock $(lock_id)

## formate tf tg code
fmt:
	@terraform fmt -recursive
	@terragrunt hclfmt

## Setup terraform plugin cache directory
set-plugin-cache:
	@echo 'plugin_cache_dir   = "$$HOME/.terraform.d/plugin-cache"' > ~/.terraformrc

## Set terraform and terragrun version
tf:
	@tfswitch
	@tgswitch

build-npm-install:
	@rm -rf node_modules build .next
	@npm ci --prod

## Website
build:
	@echo "----- Setting up environment variables -----"
	@bash ./set_build_vars.sh /$(_PROJECT)/$(_ENV)
	@echo "----- Creating build -----"
	@npm run build
	@rm -rf .env

publish-build:
	@$(eval BUCKET_NAME=$(shell make target-output target=us-west-2/s3-airports | jq -r '.id.value'))
	@cp build/assets/sitemap.xml build/
	@cp build/assets/robots.txt build/
	@echo "----- Synching build -----"
	@aws --profile $(_AWS_PROFILE) s3 sync build/ s3://$(BUCKET_NAME)/ --delete --cache-control max-age=604800,public
	@echo "----- Adjusting cache -----"
	@aws --profile $(_AWS_PROFILE) s3 cp build/index.html s3://$(BUCKET_NAME)/index.html --metadata-directive REPLACE --cache-control max-age=0,no-cache,no-store,must-revalidate

lambda-npm-install:
	@cd lambda/ \
		&& rm -rf node_modules nodejs \
		&& npm i --only=prod \

## Lambda
ZIP_FILES=$(TF_lambda_source_code_file) $(TF_lambda_layer_node_modules_file)
package-lambda-artifacts:
	@rm -rf $(ZIP_FILES)
	@echo "----- $(TF_lambda_source_code_file) -----"
	@zip ./$(TF_lambda_source_code_file) -q -r lambda -x "lambda/node_modules*"
	@cd lambda/ \
		&& mkdir -p nodejs && cp -r node_modules nodejs/ \
		&& echo "----- $(TF_lambda_layer_node_modules_file) -----" \
		&& zip ../$(TF_lambda_layer_node_modules_file) -q -r nodejs \
		&& if [ "${CI}" == true ]; then echo "Not removing node modules folders"; else rm -rf nodejs node_modules; fi

ZIP_FILES=$(TF_lambda_website_and_contentful_preview_source_code_file)
package-website-and-contentful-preview-lambda-artifacts:
	@rm -rf $(TF_lambda_website_and_contentful_preview_source_code_file)
	@echo "----- Setting up environment variables -----"
	@bash ./set_build_vars.sh /$(_PROJECT)/$(_ENV)
	@echo "----- Creating Serverless code -----"
	@npm run build:serverless
	@echo "----- Setting up Assets for packaging -----"
	@cp -r public .next/standalone
	@cp -r .next/static .next/standalone/.next
	@echo "----- Creating serverless packages -----"
	@npm run -y build:serverless:pack
	@echo "----- Preparing serverless package for lambda -----"
	@$(eval CODE_DIR=serverless)
	@cd next.out && \
	 	unzip -q -o code.zip -d $(CODE_DIR) && \
		unzip -q -o dependenciesLayer && \
		mv nodejs/node_modules $(CODE_DIR) && \
		cd $(CODE_DIR) && \
		zip -r -q ../../$(TF_lambda_website_and_contentful_preview_source_code_file) .
	@rm -rf .env

ZIP_FILES=$(TF_lambda_source_code_file) $(TF_lambda_layer_node_modules_file) $(TF_lambda_website_and_contentful_preview_source_code_file)
publish-lambda-artifacts:
	@echo "------ Publishing Lambda Artifacts in $(_AWS_REGION) ------"
	@for FILE in $$ZIP_FILES; \
		do echo "------ Publishing $$FILE ------"; \
	    aws s3 cp $$FILE s3://$(TF_prefix)-lambda-artifacts/ --profile $(_AWS_PROFILE); \
	done

publish-website-lambda-artifacts:
	@echo "------ Publishing Lambda Artifacts in $(_AWS_REGION) ------"
	@echo "------ Publishing $(TF_lambda_website_and_contentful_preview_source_code_file) ------"
	@aws s3 cp $(TF_lambda_website_and_contentful_preview_source_code_file) s3://$(TF_prefix)-lambda-artifacts/ --profile $(_AWS_PROFILE)

plan-deploy-website:
	@cd $(TERRAGRUNT_DIR)/$(_AWS_REGION)/lambda-website && terragrunt plan

deploy-website:
	@cd $(TERRAGRUNT_DIR)/$(_AWS_REGION)/lambda-website && terragrunt apply -auto-approve

invalidate-index-cache:
	@$(eval CLOUDFRONT_ID=$(shell make target-output target=us-east-1/cloudfront-airports | jq -r '.cloudfront_id.value'))
	@$(eval INVALIDATION_ID=$(shell aws --profile $(_AWS_PROFILE) cloudfront create-invalidation --distribution-id $(CLOUDFRONT_ID) --paths "/index.html"  | jq -r '.Invalidation.Id'))
	@echo "---- Waiting for Cloudfront cache invalidation ----"
	@aws --profile $(_AWS_PROFILE) cloudfront wait invalidation-completed --distribution-id $(CLOUDFRONT_ID) --id $(INVALIDATION_ID)
	@echo "---- Wait is over, Invalidation done! ----"

invalidate-cache:
	@$(eval CLOUDFRONT_ID=$(shell make target-output target=us-east-1/cloudfront-airports | jq -r '.cloudfront_id.value'))
	@$(eval INVALIDATION_ID=$(shell aws --profile $(_AWS_PROFILE) cloudfront create-invalidation --distribution-id $(CLOUDFRONT_ID) --paths "/*"  | jq -r '.Invalidation.Id'))
	@echo "---- Waiting for Cloudfront cache invalidation ----"
	@aws --profile $(_AWS_PROFILE) cloudfront wait invalidation-completed --distribution-id $(CLOUDFRONT_ID) --id $(INVALIDATION_ID)
	@echo "---- Wait is over, Invalidation done! ----"
