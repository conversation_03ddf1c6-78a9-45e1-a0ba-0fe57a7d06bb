{"configurations": [{"type": "aws-sam", "request": "direct-invoke", "name": "lambda:HelloWorldFunction (nodejs16.x)", "invokeTarget": {"target": "template", "templatePath": "${workspaceFolder}/lambda/template.yaml", "logicalId": "HelloWorldFunction"}, "lambda": {"payload": {}, "environmentVariables": {}, "runtime": "nodejs22.x"}}, {"type": "aws-sam", "request": "direct-invoke", "name": "API lambda:HelloWorldFunction (nodejs16.x)", "invokeTarget": {"target": "api", "templatePath": "${workspaceFolder}/lambda/template.yaml", "logicalId": "HelloWorldFunction"}, "api": {"path": "/hello", "httpMethod": "get", "payload": {"json": {}}}, "lambda": {"runtime": "nodejs22.x"}}, {"type": "aws-sam", "request": "direct-invoke", "name": "lambda:HelloWorldFunction (nodejs16.x)", "invokeTarget": {"target": "template", "templatePath": "${workspaceFolder}/lambda/template.yaml", "logicalId": "HelloWorldFunction"}, "lambda": {"payload": {}, "environmentVariables": {}, "runtime": "nodejs22.x"}}, {"type": "aws-sam", "request": "direct-invoke", "name": "API lambda:HelloWorldFunction (nodejs16.x)", "invokeTarget": {"target": "api", "templatePath": "${workspaceFolder}/lambda/template.yaml", "logicalId": "HelloWorldFunction"}, "api": {"path": "/hello", "httpMethod": "get", "payload": {"json": {}}}, "lambda": {"runtime": "nodejs22.x"}}, {"type": "aws-sam", "request": "direct-invoke", "name": "lambda:HelloWorldFunction (nodejs16.x)", "invokeTarget": {"target": "template", "templatePath": "${workspaceFolder}/lambda/template.yaml", "logicalId": "HelloWorldFunction"}, "lambda": {"payload": {}, "environmentVariables": {}, "runtime": "nodejs22.x"}}, {"type": "aws-sam", "request": "direct-invoke", "name": "API lambda:HelloWorldFunction (nodejs16.x)", "invokeTarget": {"target": "api", "templatePath": "${workspaceFolder}/lambda/template.yaml", "logicalId": "HelloWorldFunction"}, "api": {"path": "/hello", "httpMethod": "get", "payload": {"json": {}}}, "lambda": {"runtime": "nodejs22.x"}}]}