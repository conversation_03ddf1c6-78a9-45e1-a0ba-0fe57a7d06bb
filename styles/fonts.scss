@font-face {
  font-family: "FlamaCondensed";
  font-weight: 300;
  font-style: normal;
  src: url("/assets/fonts/FlamaCondensed-Light.eot?#iefix")
      format("embedded-opentype"),
    url("/assets/fonts/FlamaCondensed-Light.otf") format("opentype"),
    url("/assets/fonts/FlamaCondensed-Light.woff") format("woff"),
    url("/assets/fonts/FlamaCondensed-Light.ttf") format("truetype"),
    url("/assets/fonts/FlamaCondensed-Light.svg#FlamaCondensed-Light")
      format("svg");
  font-display: fallback;
}
@font-face {
  font-family: "FlamaCondensed";
  font-weight: 400;
  font-style: normal;
  src: url("/assets/fonts/FlamaCondensed-Basic.eot?#iefix")
      format("embedded-opentype"),
    url("/assets/fonts/FlamaCondensed-Basic.otf") format("opentype"),
    url("/assets/fonts/FlamaCondensed-Basic.woff") format("woff"),
    url("/assets/fonts/FlamaCondensed-Basic.ttf") format("truetype"),
    url("/assets/fonts/FlamaCondensed-Basic.svg#FlamaCondensed-Basic")
      format("svg");
  font-display: fallback;
}
@font-face {
  font-family: "FlamaCondensed";
  font-weight: 500;
  font-style: normal;
  src: url("/assets/fonts/FlamaCondensed-Book.eot?#iefix")
      format("embedded-opentype"),
    url("/assets/fonts/FlamaCondensed-Book.otf") format("opentype"),
    url("/assets/fonts/FlamaCondensed-Book.woff") format("woff"),
    url("/assets/fonts/FlamaCondensed-Book.ttf") format("truetype"),
    url("/assets/fonts/FlamaCondensed-Book.svg#FlamaCondensed-Book")
      format("svg");
  font-display: fallback;
}
@font-face {
  font-family: "FlamaCondensed";
  font-weight: 600;
  font-style: normal;
  src: url("/assets/fonts/FlamaCondensed-Semibold.eot?#iefix")
      format("embedded-opentype"),
    url("/assets/fonts/FlamaCondensed-Semibold.otf") format("opentype"),
    url("/assets/fonts/FlamaCondensed-Semibold.woff") format("woff"),
    url("/assets/fonts/FlamaCondensed-Semibold.ttf") format("truetype"),
    url("/assets/fonts/FlamaCondensed-Semibold.svg#FlamaCondensed-Semibold")
      format("svg");
  font-display: fallback;
}
