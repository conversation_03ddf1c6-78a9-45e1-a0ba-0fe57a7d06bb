.home-carousel {
  position: relative;
  .slick-arrow {
    display: none !important;
  }
  .slick-dots {
    bottom: 40px;
  }
  .slide-item {
    position: relative;
    &::after {
      content: "";
      position: absolute;
      left: 0;
      top: 0;
      width: 100%;
      height: 100%;
      background: linear-gradient(
        90.28deg,
        rgba(0, 0, 0, 0) 16.04%,
        #000000 99.33%
      );
      transform: matrix(-1, 0, 0, 1, 0, 0);
    }
    img {
      position: absolute;
      object-fit: cover;
      height: 100%;
      width: 100%;
    }
  }
  .container {
    height: 635px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    position: relative;
    z-index: 1;
    .slide-content {
      max-width: 577px;
    }
    h2 {
      font-size: 64px;
      color: $white;
    }
    p {
      color: $white;
      font-size: 18px;
      line-height: 148%;
      @media screen and (min-width: 993px) {
        margin-bottom: 30px;
      }
    }
  }

  @media screen and (max-width: 992px) {
    .slick-dots {
      bottom: 30px;
    }
    .slide-item {
      overflow: hidden;
      &::after {
        background: linear-gradient(
          359.4deg,
          #101717 0%,
          #070a0a 60%,
          rgba(4, 6, 6, 0.38) 70%,
          rgba(0, 0, 0, 0) 99.51%
        );
      }
      img {
        height: auto;
        width: 142%;
        max-width: none;
        right: -12%;
      }
    }
    .container {
      height: 590px;
      justify-content: flex-end;
      padding-bottom: 60px;
      h2 {
        font-size: 44px;
      }
      p {
        font-size: 14px;
      }
    }
  }
}

.assets-section {
  padding: 96px 0 0;
  @media screen and (max-width: 767px) {
    padding: 48px 0 40px;
    .assets {
      margin: 0;
      padding-bottom: 40px;
    }
  }
}

/* Highlights section ***********/
.highlights-section {
  padding: 80px 0;

  .flex-box h2 {
    font-size: 44px;
    color: $primary;
  }
  p {
    color: $gray03;
  }

  @media screen and (max-width: 767px) {
    padding: 20px 0 40px;
    margin: 0;
    p {
      margin: 0;
    }
    .flex-box {
      width: 50%;
      padding: 20px 10px;
    }
  }
}

/* Why URW section ***********/
.why-urw-section {
  background: $gray04 url(/assets/images/section-bg.svg) no-repeat left top;
  padding: 80px 0;
  .flex-box {
    padding: 40px 20px;
    position: relative;
    @include transition(all);

    .why-icon {
      text-align: center;
      margin-bottom: 30px;
    }
    h3 {
      color: $gray03;
      text-transform: uppercase;
      @include transition(color);
    }
    p {
      color: $gray03;
      margin: 0;
    }
  }

  .carousel-logo {
    text-align: center;
    margin-bottom: 15px;
  }

  @media screen and (min-width: 768px) {
    .flex {
      background-color: $white;
      align-items: inherit;
      .flex-box {
        width: 33.33%;
        &::after {
          content: "";
          width: 1px;
          height: 90%;
          background-color: $gray01;
          position: absolute;
          left: 0;
          top: 5%;
        }
        &::before {
          content: "";
          width: 90%;
          position: absolute;
          left: 5%;
          bottom: 0;
          @include transition(all);
        }
        &:nth-child(1),
        &:nth-child(4) {
          &::after {
            display: none;
          }
        }
        &:nth-child(1),
        &:nth-child(2),
        &:nth-child(3) {
          &::before {
            border-bottom: solid 1px $gray01;
          }
        }
        &:hover {
          @include shadow;
          &::before {
            width: 100%;
            height: 0;
            left: 0;
            border-bottom: solid 8px $primary;
          }
          h3 {
            color: $primary;
          }
        }
      }
    }
  }

  @media screen and (max-width: 767px) {
    padding: 40px 0;
    background-size: 350px auto;
    .title {
      margin-bottom: 20px;
    }
    .slick-slider {
      padding-bottom: 40px;
    }
    .flex-box {
      background-color: $white;
      border-bottom: solid 8px $primary;
      img {
        margin: 0 auto;
      }
      h3 {
        font-size: 24px;
        color: $primary;
      }
      p {
        font-size: 16px;
      }
    }
  }
}
