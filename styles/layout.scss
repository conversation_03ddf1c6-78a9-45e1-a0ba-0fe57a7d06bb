/*======================================================================
Site Name: URW Airports - Reinventing the Travel Experience
Description: URW Airports has a proven track record of delivering tailored and extraordinary travel experiences that drive commercial revenue.
Version: 1.0
------------------------------------------------------------------------
Layout Width: 100%
Container Width: 1170px
Responsive Layout: Yes
=======================================================================*/
@import "variables";
@import "fonts";
@import "mixins";
@import "slick";
@import "home";
@import "portfolio";

/******** Reset Css ***********/
* {
  @include border-box;
  --gray-lte: #e4e4e4;
}

html {
  scroll-behavior: smooth;
}
/********* Clear floats *********/
.container:before,
.container:after,
.row:before,
.row:after,
ul:before,
ul:after {
  content: " ";
  display: table;
}

.container:after,
.row:after,
ul:after {
  clear: both;
}

/******** Common Css ***********/
body {
  @include common;
  margin: 0;
  padding: 100px 0 0 0;
}

a {
  color: $base;
  text-decoration: none;
  @include transition(all);

  &:hover {
    color: $primary;
    text-decoration: none;
  }

  &:focus-visible {
    outline: 3px solid $primary;
  }
}

img {
  border: 0;
  vertical-align: middle;
  max-width: 100%;
  height: auto;
}

iframe {
  border: 0;
  vertical-align: middle;
  max-width: 100%;
}

h1,
h2,
h3 {
  font-family: $font-family-primary;
  font-weight: $font-weight;
  margin: 0 0 15px;
  line-height: 110%;
  color: $secondary;
}

h1 {
  font-size: 64px;
}

h2 {
  font-size: 44px;
  text-transform: uppercase;
}

h3 {
  font-size: 24px;
}

h4 {
  font-size: 20px;
  font-weight: 400;
}

p {
  margin: 0 0 20px;
}

select {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  outline: 0;
  box-shadow: none;
  border: 0;
  background-image: none;
  cursor: pointer;
  flex: 1;
  background: $white;
  font-family: $font-family;
  font-size: 12px;
}

/* Remove IE arrow */
select::-ms-expand {
  display: none;
}

/* Custom Select */
.select {
  @include select;
  @include border-radius(2px);

  &:after {
    font-family: "icomoon";
    content: "\e903";
    font-size: 5px;
    position: absolute;
    top: 9px;
    right: 10px;
    pointer-events: none;
    @include transition(all);
  }

  &:hover::after {
    color: $primary;
  }
}

.checkbox {
  margin: 0 0 40px;

  input {
    @include initial;
  }

  label {
    position: relative;
    cursor: pointer;
    font-size: 12px;
    color: $secondary;
    padding: 0 0 0 26px;
    display: block;
    max-width: 560px;

    &:before {
      content: "";
      -webkit-appearance: none;
      background-color: transparent;
      position: absolute;
      left: 0;
      top: 0;
      border: 1px solid $gray09;
      cursor: pointer;
      width: 14px;
      height: 14px;
      @include transition(all);
    }
  }

  // input:checked + label {
  //   background-color: $secondary;
  // }

  input:checked + label:after {
    content: "";
    display: block;
    position: absolute;
    top: 3px;
    left: 6px;
    width: 3px;
    height: 7px;
    border: solid $white;
    border-width: 0 1px 1px 0;
    transform: rotate(45deg);
    @include transition(all);
  }

  input:checked + label:before {
    background-color: $primary;
    border-color: transparent;
  }

  @media screen and (max-width: 767px) {
    margin-bottom: 20px;
  }
}

.fluid {
  width: 100%;
}

.row {
  margin: 0 -15px;
}

.container {
  max-width: $container-width;
  margin: 0 auto;
  min-width: 280px;
  padding-left: $padding;
  padding-right: $padding;
}

.section {
  clear: both;
  padding-top: $padding * 2;
  padding-bottom: $padding * 2;
}

.pull-left {
  float: left;
}

.pull-right {
  float: right;
}

.nm {
  margin: 0;
}

.ml-5 {
  margin-left: 5px;
}

.ml-10 {
  margin-left: 10px;
}

.w100 {
  width: 100%;
  position: relative;
}

.flex {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}
.flex-reverse {
  display: flex;
  align-items: center;
  flex-direction: row-reverse;
}
.flex-middle {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: center;
  flex-direction: column;
}
.flex-space-between {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  align-items: center;
}
.flex-space-around {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-around;
  align-items: center;
  text-align: center;
}

.inline-block {
  display: inline-block;
}

.v-align {
  vertical-align: middle;
}

.icon {
  position: absolute;
  left: 12px;
  top: 12px;
}

.text-center {
  text-align: center;
}

/*Button Css*/
.btn {
  @include btn;
  background-color: $primary;
  span {
    font-size: 20px;
    display: block;
    padding-right: 27px;
    @include transition(all);
    background: url(/assets/images/arrow-right.svg) no-repeat right center;
    background-size: auto 20px;
  }

  &:hover {
    color: $white;
    background-color: $primaryHover;
    span {
      padding-right: 32px;
    }
  }
}

.btn-primary {
  @include btn;
  font-size: 20px;
  background: $primary url(/assets/images/arrow-right.svg) no-repeat 77% 53%;
  background-size: auto 20px;
  position: relative;
  @include transition(all);
  padding: 12px 52px 12px 24px;

  &:hover {
    color: $white;
    background-color: $primaryHover;
    background-position: 82% 53%;
  }
}

.btn-secondary {
  background-color: $secondary;
  color: $white;

  &:hover {
    background-color: $primary;
  }
}

.btn-outline {
  @include btn;
  border: solid 1px $gray;
  font-family: $font-family;
  display: flex;
  justify-content: center;
  i {
    width: 22px;
    height: 22px;
    margin: 0 0 0 5px;
    background: url(/assets/images/arrow-link.svg) no-repeat right center;
    background-size: auto 100%;
    @include transition(all);
  }

  &:hover {
    color: $white;
    background-color: $base;

    i {
      margin: -2px -2px 0 7px;
    }
  }
}

.btn-outline-black {
  border: solid 1px $gray;
  background: transparent;
  color: $secondary;

  &:hover {
    color: $white;
    background-color: $primary;
    border-color: transparent;
  }
}

/*Link*/
.link {
  position: relative;
  text-decoration: none;
  display: inline-block;
  color: $secondary;
  transition: color ease 0.3s;

  &::before,
  &::after {
    content: "";
    position: absolute;
    background-color: $secondary;
    height: 1px;
    left: 0;
    bottom: 0;
  }

  &::before {
    width: 0%;
    transition: width ease 0.4s;
  }

  &::after {
    width: 100%;
    transition: all ease 0.6s;
  }

  &:hover {
    &::before {
      width: 100%;
      background-color: $primary;
    }

    &::after {
      left: 100%;
      width: 0%;
      transition: all ease 0.2s;
    }
  }
}

.link-underline {
  color: $secondary;
  text-decoration: none;
  position: relative;
  background: linear-gradient(180deg, currentColor 1px, transparent 0);
  background-repeat: no-repeat;
  background-position: 100% 100%;
  background-size: 0 1px;
  transition: background-size 0.3s;
  padding-bottom: 1px;
  cursor: pointer;
  &:hover {
    color: $primary;
    background-image: linear-gradient(180deg, currentColor 1px, transparent 0);
    background-size: 100% 1px;
    background-position: 0 100%;
  }
}
// BusinessOpurtunityForm

.business-form-box {
  padding-left: 15px;
  padding-right: 15px;
  .business-form {
    background-color: $gray04;
    padding: 50px 0;
  }
  .col-6 {
    padding-left: 60px;
    padding-right: 60px;
  }
  .col-6-border {
    border-left: solid 1px var(--gray-lte);
  }
  .checkbox-list {
    margin-bottom: 40px;
  }
  .checkbox {
    margin-bottom: 15px;
  }
  h4 {
    margin: 30px 0 15px;
  }
  h5 {
    margin: 0 0 15px;
  }

  @media screen and (max-width: 992px) {
    .business-form {
      padding: 40px 0;
    }
  }

  @media screen and (max-width: 767px) {
    padding: 32px 16px;
    margin-bottom: 48px;
    .col-6 {
      width: 100%;
      padding-left: 30px;
      padding-right: 30px;
    }
    .col-6-border {
      border-left: none;
    }
  }
}
.span-subtitle-2 {
  font-size: 14px;
  color: #bfbfbf;
  margin-bottom: 20px;
  display: block;
}
.business-btn-primary {
  @include btn;
  font-size: 20px;
  background: $primary url(/assets/images/arrow-right.svg) no-repeat 77% 53%;
  background-size: auto 20px;
  position: relative;
  @include transition(all);
  padding: 12px 52px 12px 24px;
  margin-top: 20px;

  &:hover {
    color: $white;
    background-color: $primaryHover;
    background-position: 82% 53%;
  }
}

.business-form-control {
  -moz-appearance: textfield !important;
  appearance: textfield !important;
  font-family: $font-family;
  font-size: 15px;
  position: relative;
  background: transparent;
  border: none;
  border-radius: 0;
  border-bottom: solid 1px $gray03;
  height: auto;
  padding: 8px 0;
  width: 100%;
  display: block;
  z-index: 100;
  &:focus,
  &.has-value {
    box-shadow: none;
    outline: none;
    ~ label {
      top: 0;
      opacity: 1;
      font-size: 12px;
      color: $gray03;
      border-bottom-color: $primary;
      &:after {
        visibility: visible;
        width: 100%;
        left: 0;
      }
    }
  }
}
.business-select {
  cursor: pointer;
  width: 95%;
}
.two-colum {
  display: grid;
  grid-template-columns: 2fr 1fr;
  // grid-gap: 10px;
  align-items: center;
}
.business-form-submit {
  position: relative;

  span {
    position: absolute;
    left: 0;
    top: 0;
    width: 30%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: $primary;
    overflow: hidden;
  }
  .loader {
    width: 6px;
    height: 6px;
    border-radius: 50%;
    display: inline-block;
    position: relative;
    color: #fff;
    left: -125px;
    animation: shadowRolling 2s linear infinite;
  }
}

.radio-group {
  display: flex;

  margin-bottom: 20px;
  .radio {
    margin-right: 12px;
  }
  label {
    margin-right: 12px;
    font-size: 13px;
    color: #777;
  }
}

/*Form*/
.form-group {
  position: relative;
  padding-top: 16px;
  margin-bottom: 40px;
  z-index: 0;

  &.pt-30{
    padding-top: 30px;
  }

  label {
    font-size: 15px;
    position: absolute;
    top: 20px;
    left: 0;
    bottom: 0;
    width: 100%;
    font-weight: 300;
    opacity: 0.5;
    cursor: text;
    transition: 0.2s ease all;
    margin: 0;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
    &:after {
      content: "";
      position: absolute;
      bottom: 0;
      left: 50%;
      height: 2px;
      width: 10px;
      visibility: hidden;
      background-color: $gray03;
      transition: 0.2s ease all;
    }
  }
  .form-control {
    font-family: $font-family;
    font-size: 15px;
    position: relative;
    background: transparent;
    border: none;
    border-radius: 0;
    border-bottom: solid 1px $gray03;
    height: auto;
    padding: 8px 0;
    width: 100%;
    display: block;
    z-index: 100;
    &:focus,
    &.has-value {
      box-shadow: none;
      outline: none;
      ~ label {
        top: 0;
        opacity: 1;
        font-size: 12px;
        color: $gray03;
        border-bottom-color: $primary;
        &:after {
          visibility: visible;
          width: 100%;
          left: 0;
        }
      }
    }
  }
  textarea.form-control {
    height: 80px;
    resize: none;
    z-index: 100;
  }
  .tooltip {
    position: absolute;
    right: 0;
    top: 19px;
  }

  @media screen and (max-width: 767px) {
    margin-bottom: 24px;

    &.pt-30{
      padding-top: 50px;
    }
  }
}

.errorText {
  color: #d8000c;
  border-radius: 3px;
  margin-bottom: 20px;
  padding: 10px 0;
  font-size: 12px;
  margin-top: -40px;
  @media screen and (max-width: 767px) {
    margin-top: -30px;
  }
}
.mg-tp-30 {
  margin-top: 30px;
}

.form-group-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 30px;
  p {
    font-size: 14px;
    margin: 0;
  }
  @media screen and (max-width: 767px) {
    flex-direction: column;
    align-items: flex-start;
    p {
      font-size: 12px;
      margin-bottom: 30px;
    }
    // .btn-primary {
    //   width: 100%;
    //   background-image: none;
    //   padding: 12px;
    // }
  }
}

.form-submit {
  position: relative;

  span {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: $primary;
    overflow: hidden;
  }
  .loader {
    width: 6px;
    height: 6px;
    border-radius: 50%;
    display: inline-block;
    position: relative;
    color: #fff;
    left: -100px;
    animation: shadowRolling 2s linear infinite;
  }
}

/*Coloum*/
.col-1,
.col-2,
.col-3,
.col-4,
.col-5,
.col-6,
.col-7,
.col-8,
.col-9,
.col-10,
.col-11,
.col-12 {
  @include col;
}
.col-12 {
  width: 100%;
}

.col-11 {
  width: 91.66666667%;
}

.col-10 {
  width: 83.33333333%;
}

.col-9 {
  width: 75%;
}

.col-8 {
  width: 66.66666667%;
}

.col-7 {
  width: 58.33333333%;
}

.col-6 {
  width: 50%;
}
.jfk-col-6 {
  width: 100%;
}

.col-5 {
  width: 41.66666667%;
}

.col-4 {
  width: 33.33333333%;
}

.col-3 {
  width: 25%;
}

.col-2 {
  width: 16.66666667%;
}

.col-1 {
  width: 8.33%;
}

/********** Common Elements **********/
/*Tag*/
.tag {
  border: 1px solid $secondary;
  border-radius: 32px;
  font-size: 12px;
  display: inline-block;
  padding: 5px 14px;
  margin: 6px 3px;
  &.current {
    background-color: $secondary;
    color: $white;
  }
}

/*Video*/
.video {
  width: 100%;
  margin-bottom: 20px;
  iframe {
    width: 100%;
    display: block;
  }
}

/* List ***********/
.list {
  margin: 30px 0 0 0;
  padding: 0;
  list-style: none;
  li {
    background: url(/assets/images/arrow-red.svg) no-repeat left 2%;
    margin-bottom: 15px;
    padding-left: 30px;
  }
  @media screen and (max-width: 767px) {
    margin-top: 20px;
    li {
      background-size: 18px;
      padding-left: 24px;
    }
  }
}

/********** Home Page **********/
.header {
  position: fixed;
  left: 0;
  top: 0;
  width: 100%;
  z-index: 10;
  padding: 18px 0;
  background-color: $white;
  @include transition(all);
  .flex {
    min-width: 100%;
    justify-content: space-between;
    align-items: center;
  }
  .logo {
    max-width: 178px;
  }
  &.nav-up {
    top: -100px;
  }
}

.menu {
  @include no-list;
  li {
    font-family: $font-family-primary;
    text-transform: uppercase;
    display: inline-block;
    padding: 0 20px;
    position: relative;
    .active {
      color: $primary;
    }
  }
}

.header-btn {
  .btn-secondary {
    border: solid 1px transparent;
    margin-left: 20px;
  }
}

/* Assets section ***********/

.title {
  text-align: center;
  margin-bottom: 40px;
  p {
    margin: 0;
  }
  @media screen and (max-width: 767px) {
    margin-bottom: 30px;
  }
}

.assets {
  margin: 0 -12px;
  display: flex;
  flex-wrap: wrap;
}

.card {
  margin: 12px;
  width: calc(50% - 24px);
  background-color: $gray01;
  position: relative;
  &:first-child {
    width: 100%;
  }
  // &:last-child {
  //   width: 100%;
  // }

  & > a {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
  }

  .card-img {
    width: 100%;
    height: 280px;
    position: relative;
    overflow: hidden;
    img {
      position: absolute;
      object-fit: cover;
      height: 100%;
      width: 100%;
      @include transition(all);
    }
  }
  .card-info {
    background-color: $gray01;
    padding: 16px 24px;
    display: flex;
    align-items: center;
    min-height: 118px;
    .card-logo {
      margin-right: 20px;
      @include grayscale;
    }
    .card-text {
      padding-right: 60px;
      p,
      h3,
      h4 {
        margin: 8px 0;
      }
      p {
        font-size: 12px;
        color: $gray02;
      }
      h3 {
        font-size: 16px;
        color: $secondary;
        font-family: $font-family;
        font-weight: bold;
      }
      h4 {
        font-size: 14px;
        font-weight: normal;
        color: $gray03;
      }
    }
    .btn {
      position: absolute;
      right: 0;
      bottom: 0;
      height: 54px;
      padding: 13px 17px 12px 10px;
      overflow: hidden;
      display: flex;
      span {
        max-width: 0;
        overflow: hidden;
        white-space: nowrap;
        text-indent: 99px;
      }
      &:hover {
        padding: 13px 20px 12px;
        span {
          overflow: visible;
          text-indent: 0;
          max-width: 115px;
        }
      }
    }
  }
  &:hover {
    .card-img {
      img {
        @include scale(1.05);
      }
    }
  }
  @media screen and (max-width: 767px) {
    width: 100%;
    margin: 0;
    .card-info {
      padding: 10px 16px 50px;
      .card-logo {
        max-width: 52px;
        margin-right: 16px;
      }
      .card-text {
        padding: 0;
        p,
        h3,
        h4 {
          margin: 6px 0;
        }
        p {
          font-size: 10px;
        }
        h3 {
          font-size: 14px;
          color: $secondary;
        }
      }
      .btn,
      .btn:hover {
        width: auto;
        height: 42px;
        padding: 8px 16px;
        align-items: center;
        span {
          max-width: none;
          text-indent: inherit;
          line-height: 1;
        }
        i {
          display: none;
        }
      }
    }
  }
}
.card-explore {
  min-height: 360px;
  .card-img {
    height: 100%;
    @media screen and (max-width: 767px) {
      padding-bottom: 100%;
    }
  }
  .card-explore-info {
    position: absolute;
    top: 0;
    height: 100%;
    width: 100%;
    padding: 40px;
    display: flex;
    align-items: center;
    background: linear-gradient(
      89.24deg,
      rgba(0, 0, 0, 0.7) 0.47%,
      rgba(0, 0, 0, 0.63) 63.03%,
      rgba(0, 0, 0, 0) 99.18%
    );
  }
  h2 {
    color: $white;
    max-width: 300px;
    margin-bottom: 30px;
  }
  @media screen and (max-width: 767px) {
    min-height: 398px;
  }
}

/* Footer ***********/
.footer {
  background: $secondary url(/assets/images/footer-bg.png) no-repeat left bottom;
  background-size: 50% auto;
  color: $white;
  padding: 60px 0 25px;
  ul,
  .footer-bottom {
    a {
      color: $white;
      @include link;
      &:hover {
        @include link-hover;
      }
    }
  }
  .footer-top {
    display: flex;
    padding-bottom: 80px;
  }
  .footer-left {
    min-width: 225px;
    .social-links {
      display: flex;
      justify-content: center;
      margin: 20px 0;
      a {
        width: 48px;
        height: 48px;
        border: solid 1px $gray02;
        margin: 10px;
        padding: 10px;
        &:hover {
          background-color: $base;
        }
      }
    }
  }
  .footer-portfolio {
    padding: 0 80px;
    h4 {
      font-size: 16px;
      color: $gray02;
    }
    ul {
      font-size: 14px;
      @include no-list;
      display: flex;
      flex-wrap: wrap;
      li {
        width: 50%;
        margin: 10px 0;
      }
    }
  }
  .footer-bottom {
    border-top: solid 1px $gray05;
    padding: 20px 0;
    font-size: 12px;
    display: flex;
    .page-links {
      margin-right: auto;
      a {
        margin-right: 20px;
      }
    }
  }

  @media screen and (max-width: 992px) {
    padding: 40px 0 0;

    .footer-top {
      flex-wrap: wrap;
      padding-bottom: 30px;
    }
    .footer-left {
      margin: 0 auto;
      .footer-logo {
        max-width: 210px;
        margin: 0 auto;
      }
    }
    .footer-portfolio {
      padding: 0;
      h4 {
        margin-bottom: 10px;
      }
      ul {
        li {
          width: 100%;
          margin: 10px 0;
        }
      }
    }
    .footer-btn {
      margin-top: 22px;
      width: 100%;
      .btn-outline {
        font-size: 14px;
        i {
          width: 19px;
          height: 19px;
        }
      }
    }
    .footer-bottom {
      display: block;
      text-align: center;
      padding: 30px 0;
      .page-links {
        margin-bottom: 20px;
        a {
          margin: 5px;
          font-size: 12px;
          white-space: nowrap;
          display: inline-block;
        }
      }
    }
  }
}

// Common Components //
/* Awards section ***********/
.awards-section {
  padding: 100px 0;
  @media screen and (max-width: 767px) {
    padding: 50px 0;
  }
}
.awards-carousel {
  padding-bottom: 50px;
  @media screen and (max-width: 767px) {
    padding-bottom: 40px;
  }
  .slick-slide {
    padding: 0 10px;
  }
}
.awards-item {
  border: 1px solid $gray;
  padding: 20px 10px;
  text-align: center;
  min-height: 204px;
  @include transition(all);
  .carousel-logo {
    height: 82px;
    display: flex;
    align-items: center;
    img {
      margin: 0 auto;
      @include grayscale;
    }
  }
  &:hover {
    border-color: $gray02;
    img {
      filter: grayscale(0);
      -webkit-filter: grayscale(0);
    }
  }
  h4 {
    font-size: 14px;
    color: $secondary;
    margin-bottom: 5px;
  }
  p {
    font-size: 12px;
    color: $gray02;
    margin: 0;
  }
}

/* News section ***********/
.slick-arrow {
  width: 48px;
  height: 48px;
  border: solid 1px $gray02;
  background: white;
  position: absolute;
  left: auto;
  right: 0;
  bottom: 0;
  font-size: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1;
  cursor: pointer;
  @include transition(all);

  &:hover {
    background-color: $gray01;
  }

  &:after {
    content: "";
    border-bottom-style: solid;
    border-bottom-width: 2px;
    border-right-style: solid;
    border-right-width: 2px;
    border-color: $secondary;
    display: inline-block;
    height: 8px;
    width: 8px;
    margin-right: 5px;
    @include rotate(315deg);
  }

  &.slick-prev {
    left: auto;
    right: 62px;
    &:after {
      margin: 0 0 0 5px;
      @include rotate(135deg);
    }
  }

  @media screen and (max-width: 768px) {
    width: 40px;
    height: 40px;
    &.slick-prev {
      left: auto;
      right: 54px;
    }
  }
}

.news-section {
  padding: 80px 0;
  position: relative;
  &::before {
    content: "";
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 35%;
    background-color: $gray04;
    z-index: -1;
  }
  @media screen and (max-width: 992px) {
    padding: 50px 15px;
  }
}

.news-item {
  h4 {
    border: 1px solid $gray01;
    border-radius: 32px;
    font-size: 12px;
    display: inline-block;
    padding: 5px 12px;
    margin: 20px 0 15px;
  }
  h3 {
    font-family: $font-family;
    color: $secondary;
    line-height: 140%;
  }
  p {
    color: $gray03;
  }

  @media screen and (max-width: 992px) {
    transition: none;
    transform: none;
    .news-img {
      box-shadow: none !important;
      img {
        opacity: 1;
      }
    }
    .news-info {
      display: block;
      p {
        display: none;
      }
    }
  }
}

@media screen and (min-width: 991px) {
  .news-carousel {
    .slick-slide {
      @include scale(0.8);
      @include transition(all);
      width: 760px;
      .news-img {
        background-color: black;

        img {
          opacity: 0.5;
        }
      }
      .news-info {
        display: none;
      }
      &.slick-current {
        @include scale(1);

        .news-img {
          box-shadow: 0px 4px 40px rgba(0, 0, 0, 0.18);

          img {
            opacity: 1;
          }
        }
        .news-info {
          display: block;
        }
      }
    }

    &.slick-slider .slick-arrow {
      right: calc(50% - 380px);
      bottom: 0;
      left: auto !important;
      &.slick-prev {
        right: calc(50% - 310px);
      }
    }
  }
}

.sub-container {
  text-align: left;
  border-left: solid 1px $primary;
  padding: 60px 60px 0;
  margin: 0 auto 0 0;
  h2 {
    margin-bottom: 30px;
  }

  @media screen and (min-width: 768px) {
    .news-info {
      max-width: 820px;
      h3 {
        font-size: 32px;
        line-height: 120%;
      }
      p {
        font-size: 18px;
        margin-bottom: 30px;
      }
    }
  }

  .latest-news-carousel {
    .news-img {
      height: 460px;
      overflow: hidden;
      img {
        width: 100%;
        object-fit: cover;
      }
    }
  }

  @media screen and (max-width: 767px) {
    padding: 20px 0 0 0;
    h2 {
      padding-left: 15px;
      margin-bottom: 20px;
    }
    .latest-news-carousel {
      .news-img {
        height: 200px;
      }
      .news-info {
        padding-left: 15px;
      }
      .news-item p {
        display: none;
      }
    }
  }
}

.news-tabs-section.tabs-section {
  .tabs-content {
    padding: 24px 0;
    border-bottom: solid 1px $gray10;

    a {
      cursor: pointer;
    }
  }
  .news-btn {
    text-align: center;
    padding: 50px 0;
  }
  @media screen and (max-width: 767px) {
    padding: 48px 0 0 0;
    .news-tags {
      overflow: auto;
      white-space: nowrap;
      padding-bottom: 15px;
    }
    .news-item {
      width: 50%;
      padding: 16px 12px;
      p,
      .news-link {
        display: none;
      }
    }
    .news-btn {
      padding: 20px 0 30px;
    }
  }
  @media screen and (max-width: 480px) {
    .news-item {
      width: 100%;
    }
  }
}

.news-carousel {
  .slick-arrow {
    width: 48px;
    height: 48px;
    border: solid 1px $gray02;
    position: absolute;
    left: calc(50% + 170px);
    bottom: 0;
    font-size: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1;
    cursor: pointer;
    @include transition(all);

    &:hover {
      background-color: $gray01;
    }

    &:after {
      border-bottom-style: solid;
      border-bottom-width: 2px;
      border-right-style: solid;
      border-right-width: 2px;
      content: "";
      display: inline-block;
      height: 8px;
      width: 8px;
      margin-right: 5px;
      @include rotate(315deg);
    }

    &.slick-prev {
      left: calc(50% + 100px);
      &:after {
        margin: 0 0 0 5px;
        @include rotate(135deg);
      }
    }
    @media screen and (max-width: 992px) {
      left: auto;
      right: 0;
      width: 40px;
      height: 40px;
      &.slick-prev {
        left: auto;
        right: 54px;
      }
    }
  }

  .slick-slide {
    @include scale(0.8);
    @include transition(all);

    .news-img {
      background-color: black;
      img {
        opacity: 0.5;
      }
    }

    &.slick-current {
      @include scale(1);
      .news-img {
        box-shadow: 0px 4px 40px rgba(0, 0, 0, 0.18);
        img {
          opacity: 1;
        }
      }
    }
    @media screen and (max-width: 992px) {
      transition: none;
      transform: none;

      .news-img {
        box-shadow: none !important;
        img {
          opacity: 1;
        }
      }
    }
  }
}

.news-cards {
  margin: 0 -12px;
  display: flex;
  flex-wrap: wrap;
  h3 {
    margin: 0;
    a {
      display: inline-block;
    }
  }
  h5 {
    font-size: 16px;
    color: $gray02;
    margin: 5px 0 15px;
    font-weight: 400;
  }
  p {
    margin: 0 0 4px;
  }
  .news-item {
    padding: 24px 12px;
    width: 33.33%;
  }
}
.single-page {
  h1 {
    font-family: $font-family;
    color: $secondary;
    font-size: 32px;
  }
  p {
    color: $gray03;
  }
  ul {
    color: $gray03;
    margin-bottom: 32px;
    li {
      margin-bottom: 5px;
    }
  }
  .container {
    max-width: 792px;
  }
  .single-banner-section {
    background: url(/assets/images/single-bg.svg) no-repeat center center;
    background-size: 100%;
  }
  .single-info {
    h4 {
      border: 1px solid $gray01;
      border-radius: 32px;
      font-size: 12px;
      display: inline-block;
      padding: 5px 12px;
      margin: 20px 0 15px;
    }
    .single-share {
      display: flex;
      align-items: center;
      padding-bottom: 16px;
      border-bottom: solid 1px $gray01;
      margin: 40px 0 0;
      .date {
        color: $gray02;
      }
      .share {
        margin-left: auto;
        display: flex;
        align-items: center;
        a {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 36px;
          height: 36px;
          border: solid 1px $gray09;
          margin-left: 12px;
          &:hover {
            background-color: $gray01;
          }
        }
      }
    }
  }
  .single-content {
    padding: 50px 0;
  }
  .quotes {
    border: solid 1px $gray10;
    margin-bottom: 30px;
    q {
      padding: 20px;
      font-size: 18px;
      font-style: italic;
      display: block;
    }
  }
  .single-carousel {
    padding-bottom: 65px;
    overflow: visible;
    .slick-arrow {
      bottom: 50%;
      margin-top: -24px;
      right: -70px;
      &.slick-prev {
        right: auto;
        left: -70px;
      }
    }
  }

  @media screen and (min-width: 768px) {
    p {
      font-size: 18px;
    }
    ul {
      font-size: 18px;
    }
    .quotes {
      display: flex;
      align-items: center;
      img {
        max-width: 63%;
      }
    }
  }
  @media screen and (max-width: 992px) {
    .single-carousel .slick-arrow {
      display: none !important;
    }
  }
  @media screen and (max-width: 768px) {
    h3 {
      font-size: 24px;
    }
    .single-banner-section {
      background-position: 0 0;
      background-size: 200%;
      padding-top: 20px;
    }
    .single-info .single-share {
      margin-top: 30px;
    }
    .single-content {
      padding: 30px 0;
    }
    .video {
      iframe {
        height: 200px;
      }
    }
  }
}

/* Tabs section ***********/
.tabs-section {
  padding: 96px 0 0;
  .tab-content {
    display: none;
    -webkit-animation: fadeEffect 1s;
    animation: fadeEffect 1s;
  }
  .tab-content.current {
    display: inherit;
  }
  .tabs-image {
    .tab-content {
      height: 320px;
      overflow: hidden;
      position: relative;
      background-color: $gray;
      img {
        position: absolute;
        left: 0;
        top: 0;
        height: 100%;
        width: 100%;
        object-fit: cover;
      }
    }
  }
  .tabs-content {
    padding: 48px 0 30px;
    .tab-details {
      display: flex;
    }
    .tab-info {
      h2,
      h3 {
        margin-bottom: 25px;
      }
      h3 {
        text-transform: uppercase;
        line-height: 30px;
      }
      p {
        font-size: 18px;
      }
    }
    .logo-info {
      min-width: 280px;
      background-color: $gray04;
      text-align: center;
      padding: 40px 30px;
      h4 {
        font-size: 14px;
        text-transform: uppercase;
        margin: 15px 0 5px 0;
      }
      p {
        font-size: 12px;
        color: $gray02;
        margin: 0;
      }
    }

    .highlight-section {
      background: none;
    }
  }

  .tabs-container {
    background-color: $gray04;
    position: relative;
    height: 68px;
    z-index: 1;
    nav {
      margin: 0 auto;
      position: absolute;
      width: 100%;
      ul {
        list-style: none;
        margin: 0;
        padding: 0;
        display: flex;
        flex-direction: column;
        li {
          width: 100%;
          @include transition(all);
          cursor: pointer;
          // order: 1;
          a {
            width: 100%;
            padding: 20px;
            height: 68px;
            font-family: $font-family-primary;
            font-size: 24px;
            text-transform: uppercase;
            line-height: normal;
            display: block;
            color: $white;
            &:hover {
              color: $gray;
              background-color: $secondary;
            }
          }
          &:first-child {
            span {
              display: block;
            }
          }

          &:not(.active) {
            position: absolute;
            top: -999em;
          }
          &.active {
            background-color: $secondary;
            border-top: none;
            order: 0;
            a {
              color: $white;
            }
            span {
              display: block;
            }
          }
        }
      }
      ul.expanded {
        // li.active {
        //   &:after {
        //     top: 1em;
        //   }
        // }
        li {
          &:not(.active) {
            position: relative;
            top: auto;
          }
          &.active {
            background-color: $secondary;
            span {
              transform: rotate(180deg);
            }
          }
          &:first-child {
            span {
              display: none;
            }
            &.active {
              span {
                display: block !important;
              }
            }
          }
        }
      }
    }
    @media all and (min-width: 768px) {
      // margin: 0 -15px;
    }
  }

  @media all and (min-width: 768px) {
    .logo-info {
      height: 100%;
      margin-left: 130px;
    }
    .tabs-container {
      height: 80px;
      nav {
        ul {
          display: flex;
          flex-direction: row;
          justify-content: space-between;
          overflow: hidden;
          li {
            border-left: solid 1px $gray;
            position: relative;
            a {
              display: flex;
              align-items: center;
              justify-content: center;
              font-size: 18px;
              height: 80px;
              color: #3f3f3f;
            }
            &:first-child {
              border: none;
            }
            &:not(.active) {
              position: relative;
              top: auto;
            }
            &.active {
              // order: 1;
              &:after {
                content: "";
                width: 1px;
                height: 1px;
                border-left: 10px solid transparent;
                border-right: 10px solid transparent;
                border-bottom: 10px solid white;
                margin-left: -10px;
                position: absolute;
                left: 50%;
                bottom: 0;
                z-index: 1;
              }
            }
          }
        }
      }
    }
  }

  @media screen and (max-width: 992px) {
    .tab-details {
      flex-wrap: wrap;
      .logo-info {
        margin: 30px auto 0;
      }
    }
  }

  @media screen and (max-width: 767px) {
    padding-top: 20px;
    .tabs-image {
      .tab-content {
        height: 230px;
      }
    }
    .tabs-content {
      padding: 30px 0;
      .tab-info {
        h2 {
          font-size: 32px;
        }
        p {
          font-size: 14px;
        }
      }
      .highlight-section {
        margin: 0;
        padding-bottom: 0;
      }
    }

    .tabs-container {
      nav {
        background-color: $base;
        ul {
          -webkit-animation: fadeEffect 1s;
          animation: fadeEffect 1s;
          li {
            color: $white;
            span {
              width: 38px;
              height: 38px;
              position: absolute;
              right: 30px;
              top: 14px;
              z-index: 1;
              border: solid 1px $gray03;
              @include border-radius(50px);
              display: none;
              &::after {
                content: "";
                border-bottom-style: solid;
                border-bottom-width: 2px;
                border-right-style: solid;
                border-right-width: 2px;
                display: inline-block;
                height: 6px;
                width: 6px;
                margin-right: 5px;
                position: absolute;
                right: 9px;
                top: 13px;
                border-color: $white;
                @include rotate(46deg);
              }
            }
          }
        }
      }
    }
  }
}

/* Business section ***********/
.business-section {
  background: $gray04 url(/assets/images/section-bg-bottom.svg) no-repeat right
    bottom;
  padding: 80px 0;
  .db-img {
    width: 40%;
    box-shadow: -4px 0px 76px rgba(0, 0, 0, 0.16);
  }
  .db-info {
    width: 60%;
    padding: 40px;
    box-shadow: -4px 0px 60px rgba(0, 0, 0, 0.11);
    background-color: $white;
    p {
      font-size: 18px;
      color: $gray03;
    }
  }
  @media screen and (max-width: 767px) {
    padding: 30px 0;
    .flex-reverse {
      flex-wrap: wrap;
    }
    .db-img {
      min-width: 100%;
    }
    .db-info {
      width: 100%;
      padding: 30px;
      p {
        font-size: 16px;
      }
    }
  }
}

/* Banner section ***********/
.banner-section {
  background-color: $secondary;
  position: relative;
  min-height: 300px;
  overflow: hidden;
  display: flex;
  align-items: center;
  &::after {
    content: "";
    @include position;
    background: linear-gradient(90deg, #000000 -0.47%, rgba(0, 0, 0, 0) 83.85%);
  }
  img {
    position: absolute;
    left: 0;
    top: 0;
    object-fit: cover;
    height: 100%;
    width: 100%;
  }
  .container {
    position: relative;
    z-index: 1;
    width: 100%;
    color: $white;
    padding: 30px 15px;
    .banner-text {
      max-width: 630px;
    }
    h1 {
      text-transform: uppercase;
      margin: 15px 0;
      color: $white;
    }
    p {
      margin: 0;
    }
  }
  @media screen and (max-width: 767px) {
    min-height: 180px;
  }
}

/* Small banner section ***********/
.small-banner-section {
  margin: 64px 0;

  .banner-wrapper {
    background-color: $secondary;
    min-height: 400px;
    padding: 0 64px;
    position: relative;
    overflow: hidden;
    display: flex;
    align-items: center;
    &::after {
      content: "";
      @include position;
      background: linear-gradient(
        90deg,
        #000000 -0.47%,
        rgba(0, 0, 0, 0) 83.85%
      );
    }
    img {
      position: absolute;
      left: 0;
      top: 0;
      object-fit: cover;
      height: 100%;
      width: 100%;
    }
  }
  .banner-text {
    max-width: 330px;
    position: relative;
    z-index: 1;
    width: 100%;
    color: $white;
    padding: 30px 15px;
    h2 {
      margin: 15px 0 30px;
      color: $white;
    }
    &.w-480 {
      max-width: 480px;
    }
  }

  @media screen and (max-width: 767px) {
    margin: 32px 0 48px;
    .banner-wrapper {
      padding: 0;
      min-height: auto;
      .banner-text {
        padding: 30px 16px 38px 16px;
        max-width: 230px;
      }
    }
  }
}

// Inner Page **************/
.sub-section {
  .container {
    display: flex;
    align-items: center;
  }
  .title {
    text-align: left;
    border-left: solid 1px $primary;
    padding: 60px 60px 0;
    max-width: 765px;
    margin: 0 auto 0 0;
  }
  .title-logo {
    min-width: 170px;
  }
  p {
    margin: 0 0 20px;
    &:last-child {
      margin: 0;
    }
  }
  @media screen and (max-width: 767px) {
    .container {
      flex-wrap: wrap;
    }
    .title {
      padding: 20px 20px 0;
    }
    .title-logo {
      margin: 40px auto 0;
      max-width: 150px;
      min-width: auto;
    }
  }
}

/* Highlight section ***********/
.highlight-section {
  background-color: $gray04;
  margin: 48px 0 74px 0;
  .container {
    position: relative;
    padding-top: 40px;
    padding-bottom: 40px;
  }
  .highlight-note {
    position: absolute;
    bottom: -20px;
    font-size: 12px;
  }
  h2 {
    font-size: 48px;
    color: $primary;
  }
  p {
    color: $gray03;
    margin: 0;
  }
  @media screen and (max-width: 992px) {
    margin: 40px 0;
    .flex-space-around {
      justify-content: start;
      align-items: flex-start;
      .flex-box {
        width: 33.33%;
        padding: 25px 5px;
      }
    }
  }
  @media screen and (max-width: 767px) {
    h2 {
      font-size: 44px;
    }
    .flex-space-around {
      .flex-box {
        width: 50%;
      }
    }
  }
}

/* Download section ***********/
.download-section {
  .container {
    display: flex;
  }

  .download-action {
    min-width: 160px;
    margin-left: 80px;
    .download-doc {
      display: block;
      overflow: hidden;
      background-color: $gray06;
      color: $white;
      border-radius: 0px 8px 8px 0px;
      font-family: $font-family-primary;
      font-size: 30px;
      line-height: 36px;
      text-transform: uppercase;
      box-shadow: 6px 6px 0px $gray09;
      width: 154px;
      margin: 0 0 24px;

      span:first-child {
        display: block;
        padding: 52px 12px 12px;
      }
      span:last-child {
        display: flex;
        strong {
          font-weight: normal;
          background-color: $gray07;
          padding: 12px;
        }
        i {
          background: $gray08 url(/assets/images/icon-w.svg) no-repeat 78%
            center;
          width: 100%;
        }
      }
    }
    .btn {
      width: 100%;
      white-space: nowrap;
    }
  }
  @media screen and (max-width: 992px) {
    .container {
      flex-wrap: wrap;
      .download-action {
        @include scale(0.6);
        position: fixed;
        right: -15px;
        bottom: -35px;
        z-index: 11;
        .btn {
          span {
            font-size: 24px;
          }
        }
      }
    }
  }
}

/* Community Page ***********/
.community-section {
  h3 {
    text-transform: uppercase;
  }
  .sub-info {
    max-width: 730px;
  }
  .video {
    margin-bottom: 30px;
  }
}
.mission-section {
  margin: 80px 0;
  padding: 80px 0;
  background: $gray04 url(/assets/images/section-bg.svg) no-repeat left top;
  background-size: auto 100%;
  .flex {
    background-color: $white;
    box-shadow: -4px 0px 60px rgba(0, 0, 0, 0.11);
  }
  .mission-info {
    padding: 40px 32px;
  }
}

.zigzag-section {
  .flex:last-child {
    border-bottom: solid 1px $gray10;
    padding-bottom: 64px;
  }
  h3 {
    text-transform: uppercase;
  }
}

.partner-section {
  padding: 100px 0;
  .awards-item {
    min-height: auto;
    padding: 60px 10px;
  }
}

@media screen and (min-width: 768px) {
  .community-section {
    h2 {
      font-size: 48px;
    }
    p {
      font-size: 18px;
    }
  }
  .mission-img,
  .mission-info {
    width: 50%;
  }
  .mission-img {
    align-self: normal;
    img {
      object-fit: cover;
      width: 100%;
      height: 100%;
    }
  }

  .zigzag-section {
    h3 {
      font-size: 28px;
    }
    p {
      font-size: 18px;
    }
    .title {
      margin: 0;
    }
    .flex {
      padding: 48px 0;
    }
    .flex-reverse {
      .zigzag-info::before {
        left: 50%;
      }
    }
  }
  .zigzag-info {
    padding: 24px;
    position: relative;
    &::before {
      content: "";
      position: absolute;
      left: 0;
      top: 0;
      background-color: $gray04;
      width: 50%;
      height: 100%;
      z-index: -1;
    }
  }
  .zigzag-img,
  .zigzag-info {
    width: 50%;
  }
}

@media screen and (max-width: 767px) {
  .sub-info {
    padding-left: 15px;
  }
  .video {
    margin-bottom: 20px;
    iframe {
      height: 200px;
    }
  }
  .mission-section {
    margin: 50px 0;
    padding: 32px 0;
    background-size: auto 35%;
    .mission-info {
      padding: 30px 15px;
    }
  }
  .zigzag-section {
    .flex,
    .flex-reverse {
      flex-wrap: wrap;
      margin-bottom: 36px;
      &:last-child {
        padding-bottom: 50px;
        margin: 0;
      }
    }
  }
  .zigzag-img,
  .zigzag-video {
    position: relative;
    margin: 0 -15px;
    padding: 24px 15px 40px;
    &::before {
      content: "";
      position: absolute;
      left: 0;
      right: 0;
      top: 0;
      background-color: $gray04;
      height: 100%;
      z-index: -1;
    }
  }
  .zigzag-info {
    margin: -18px 0 0 0;
    h3 {
      font-size: 24px;
    }
  }
  .partner-section {
    padding: 50px 0;
  }
}

/* Contact Us Page ***********/
.newsletter-section {
  background-color: $gray04;
  padding: 32px 0;
  .flex {
    justify-content: center;
    h3 {
      font-size: 32px;
      margin: 0;
      text-transform: uppercase;
    }
    .form-group {
      width: 100%;
      max-width: 350px;
      margin: 0 40px;
    }
  }
  @media screen and (max-width: 767px) {
    .flex {
      flex-direction: column;
      align-items: flex-start;
      .form-group {
        margin: 20px 0;
        max-width: none;
      }
      .btn {
        width: 100%;
        height: 40px;
      }
    }
  }
}

.form-box {
  background-color: $gray04;
  padding: 64px;
  margin-bottom: 96px;
  .form {
    max-width: 790px;
    margin: 0 auto;
  }

  @media screen and (max-width: 767px) {
    padding: 32px 16px;
    margin-bottom: 48px;
    .col-6 {
      width: 100%;
    }
  }
}

.thanks-msg {
  min-height: 472px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  text-align: center;
  h2 {
    margin: 15px 0;
  }
  p {
    margin: 0 0 15px;
  }
  h4 {
    font-size: 14px;
    color: $gray02;
    margin: 0;
  }

  .check-background {
    width: 76px;
    height: 76px;
    background: $primary;
    box-shadow: 0px 0px 0px 65px rgba(255, 255, 255, 0.25),
      0px 0px 0px 65px rgba(255, 255, 255, 0.25);
    transform: scale(0.84);
    border-radius: 50%;
    animation: animateContainer 0.75s ease-out forwards 0.75s;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    margin: 16px;
    position: relative;

    &:before {
      content: "";
      position: absolute;
      width: 100%;
      height: 100%;
      border-radius: 50%;
      border: solid 10px rgba(214, 45, 32, 0.5);
    }
    svg {
      width: 28px;
      transform: translateY(0.25rem);
      stroke-dasharray: 80;
      stroke-dashoffset: 80;
      animation: animateCheck 0.35s forwards 1.25s ease-out;
      margin-bottom: 5px;
    }
  }

  @media screen and (min-width: 768px) {
    h2 {
      font-size: 48px;
      margin: 30px 0 15px 0;
    }
    p {
      font-size: 20px;
    }
    h4 {
      font-size: 20px;
    }
    .check-background {
      width: 120px;
      height: 120px;
      &:before {
        border: solid 20px rgba(214, 45, 32, 0.5);
      }
      svg {
        width: 38px;
      }
    }
  }
}

.modal-content {
  display: flex;
  margin-top: 2em;
  margin-bottom: 2em;
  background-color: $white;

  .modal-header {
    position: relative;
    margin-bottom: 30px;
    h2 {
      margin: 0;
      padding: 5px 0;
    }
    .modal-close {
      position: absolute;
      right: 0;
      top: 0;
      width: 40px;
      height: 40px;
      z-index: 1;
      background-color: $gray04;
      border: none;
      display: flex;
      justify-content: center;
      align-items: center;
      cursor: pointer;
      @include border-radius(50px);
      @include transition(background-color);
      &:hover {
        background-color: $gray01;
      }

      .icon-close {
        width: 24px;
        height: 24px;
        @include transition(all);
        position: absolute;
        background: url(/assets/images/icon-close.svg) no-repeat center center;
      }
    }
    @media screen and (max-width: 767px) {
      margin-bottom: 20px;
    }
  }

  > .col-4 {
    background-color: $secondary;
    color: $white;
    padding: 0;
    .register-info {
      padding: 48px 32px;
      h2 {
        color: $white;
      }
      p {
        font-size: 14px;
      }
      ul {
        font-size: 16px;
        margin-bottom: 0;
      }
    }
  }
  .col-8 {
    padding: 32px;
    h4 {
      font-size: 16px;
    }
    .flex-space-between {
      align-items: inherit;
      max-width: 660px;
      .flex-col {
        max-width: 50%;
      }
      .checkbox {
        margin: 0 0 12px 0;
        label {
          font-size: 14px;
          &::before {
            top: 1px;
          }
          &::after {
            top: 4px;
          }
        }
      }
    }
    .checkbox-cities {
      margin-bottom: 20px;
      .flex-space-between {
        max-width: 660px;
      }
    }
  }

  .form-group-footer {
    p {
      font-size: 12px;
    }
    .checkbox {
      margin: 0 0 5px;
    }
    .link {
      margin: 0 5px;
      font-size: 12px;
    }
  }

  @media screen and (max-width: 1024px) {
    flex-direction: column-reverse;
    > .col-4,
    > .col-8 {
      width: 100%;
      .flex-space-between .flex-col {
        max-width: none;
      }
    }
  }

  @media screen and (max-width: 767px) {
    flex-direction: column-reverse;

    > .col-8 {
      padding: 24px;
      .flex-space-between {
        flex-direction: column;
      }
      .checkbox-cities {
        margin: 5px 0 0 0;
      }
      .form-group-footer .checkbox {
        margin-bottom: 20px;
      }
    }
    .col-6 {
      width: 100%;
    }
    .col-4 {
      display: none;
    }
  }
}

.ReactModalPortal {
  .ReactModal__Overlay {
    background-color: rgb(0 0 0 / 75%) !important;
    z-index: 111;
    overflow: auto;

    visibility: hidden;

    &.ReactModal__FadeIn {
      visibility: visible;

      .ReactModal__Content {
        margin-top: 1em;
        overflow: visible !important;
        opacity: 1;
      }
    }
  }

  .ReactModal__Content {
    margin: 0 auto;
    max-width: 90%;
    inset: inherit !important;
    background: none !important;
    padding: 0 !important;
    border: none !important;
    border-radius: 0 !important;
    margin-top: 0;
    opacity: 0;
    transition: all 0.3s ease;
    height: 110%;
  }
}

/* Leasing Page ***********/
.leasing-section {
  h2 {
    max-width: 600px;
    text-transform: uppercase;
  }
  .sub-info {
    max-width: 730px;
  }
  .sub-btn {
    margin-top: 40px;
  }
}

.about-page {
  .leasing-section {
    .sub-container {
      h2 {
        margin: 0;
      }
      h2:nth-child(2) {
        margin: 0 0 30px;
        color: $primary;
      }
    }
  }
  .info-section {
    margin: 70px 0 40px;
    padding: 70px 0 48px 0;
    background-color: $gray04;
    // text-align: center;

    h3 {
      color: $primary;
    }
  }
  .green-section {
    margin: 40px 0 90px;
    text-align: center;

    p {
      text-align: left;
    }

    h2 {
      margin: 0 0 -8px;
      position: relative;
    }
    .flex {
      align-items: flex-start;
      justify-content: center;
      margin: 40px 0;
      .col-item {
        padding: 0 30px;
        img {
          max-height: 200px;
        }
        p {
          margin-top: 20px;
          padding: 60px 0 0 0;
          position: relative;
          ::after {
            content: "";
            position: absolute;
            left: 50%;
            top: 0;
            background-color: #000000;
            height: 40px;
            width: 4px;
          }
        }
      }
    }
    .btn {
      background-color: #008c38;
      &:hover {
        background-color: #151515;
      }
    }
    @media screen and (min-width: 768px) {
      .col-item {
        width: 33.33%;
      }
    }
    @media screen and (max-width: 767px) {
      .col-item {
        margin-top: 40px;
        &:first-child {
          margin: 0;
        }
        img {
          max-width: 140px;
        }
      }
    }
  }
}

.sustainable-page{ 
  .btn{
    background-color: $green;
    &:hover{
      background-color: $greenHover;
    }
  }

  .community-section {
    p{
      font-size: 16px;
    }
    h2{
      max-width: 500px;
    }
  }
  .sub-container{
    border-color: $green;
    h2 {
      margin-bottom: 20px;
    }
    h2:nth-child(2) {
      margin: 0 0 30px;
    }
    span, a{
      color: $green;
     }
     .link::before, .link::after{
      background-color: $green;
     }
  }
  .zigzag-section{
    padding-top: 40px;
    margin-top: 50px;
    position: relative;
    &::before{
      content: "";
      position: absolute;
      left: 0;
      top: 0;
      width: 100%;
      height: 100%;
      background-color: $gray12;
      z-index: -2;
    }
    h3{
      display: flex;
      align-items: center;
        img{
          max-height: 50px;
          margin-right: 15px;
        }
    }
    .flex:last-child{
      border: none;
    }
  }

  @media screen and (min-width: 768px) {
    .zigzag-section{
      margin-top: 70px;
      padding-top: 80px;
      h3{
        img{
          max-height: 60px;
        }
      }
      .zigzag-info::before {
        background-color: #EBEBEB;
      }
    }
  }
}

.leasing-zigzag-section {
  padding: 64px 0 96px 0;
  .leasing-zigzag-info {
    max-width: 900px;
    margin-bottom: 48px;
    h2:first-child {
      margin: 0;
    }
  }
  h3 {
    font-size: 28px;
    text-transform: uppercase;
  }
  p {
    font-size: 18px;
  }
  .zigzag-info::before {
    width: 70%;
  }
  .zigzag-btn {
    margin-top: 20px;
  }
  @media screen and (max-width: 767px) {
    padding: 48px 0 64px 0;
    .leasing-zigzag-info {
      margin-bottom: 24px;
      p {
        font-size: 15px;
      }
    }
    .zigzag-video {
      iframe {
        height: 200px;
      }
    }
  }
}


.supplier-diversity-section{
  h4{
    margin-top: 0;
    margin-bottom: 16px;
  }
  .sub-container{
    padding: 60px 100px 60px 30px;
    h2 {
      margin: 0;
    }
    h2:nth-child(2) {
      margin: 0 0 30px;
      color: $primary;
    }
    @media screen and (max-width: 992px) {
      width: 100%;
      padding: 20px 20px 0 20px;
    }
  }
  .form-container{
    padding: 50px 0 80px;
    @media screen and (max-width: 992px) {
      width: 100%;
      padding: 40px 0;
    }
  }
  .flex-space-between{
    align-items: flex-start;
    .flex-col{
      min-width: 50%;
    }
  }
  .title{
    text-align: left;
  }
  .checkbox {
    margin: 0 0 12px 0;
    label {
      font-size: 14px;
      &::before {
        top: 1px;
      }
      &::after {
        top: 4px;
      }
    }
  }
  .form-group-footer-left a{
    margin: 0 5px;
  }
}


/*== Tooltips ==*/
.tooltip {
  cursor: help;
  position: relative;
  z-index: 111;
  &::before {
    left: 50%;
    opacity: 0;
    position: absolute;
    z-index: -1;
    border-style: solid;
    border-width: 10px;
    border-color: $gray03 transparent transparent transparent;
    bottom: 75%;
    content: "";
    margin-left: -10px;
    transition: all 0.65s cubic-bezier(0.84, -0.18, 0.31, 1.26),
      opacity 0.65s 0.5s;
    transform: scale(0.6) translateY(-100%);
  }
  &::after {
    left: 50%;
    opacity: 0;
    position: absolute;
    z-index: -1;
    background: $gray03;
    border-radius: 4px;
    bottom: 150%;
    color: #edeff0;
    content: attr(data-tip);
    margin-left: -110px;
    width: 220px;
    padding: 10px 12px;
    transition: all 0.65s cubic-bezier(0.84, -0.18, 0.31, 1.26) 0.2s;
    transform: scale(0.6) translateY(50%);
    box-sizing: border-box;
    font-size: 12px;
  }
  &:hover {
    &::before {
      opacity: 1;
      transform: scale(1) translateY(0);
      z-index: 100;
      transition: all 0.65s cubic-bezier(0.84, -0.18, 0.31, 1.26) 0.2s;
    }
    &::after {
      opacity: 1;
      transform: scale(1) translateY(0);
      z-index: 100;
      transition: all 0.65s cubic-bezier(0.84, -0.18, 0.31, 1.26);
    }
  }
  &:focus {
    &::before {
      opacity: 1;
      transform: scale(1) translateY(0);
      z-index: 100;
      transition: all 0.65s cubic-bezier(0.84, -0.18, 0.31, 1.26) 0.2s;
    }
    &::after {
      opacity: 1;
      transform: scale(1) translateY(0);
      z-index: 100;
      transition: all 0.65s cubic-bezier(0.84, -0.18, 0.31, 1.26);
    }
  }
}
@media (max-width: 760px) {
  .tooltip {
    &::after {
      width: 180px;
      margin: 0;
      left: auto;
      right: -10px;
    }
  }
}

.page-404 {
  text-align: center;
  padding: 40px;
}

.img-404 {
  margin: 100px auto 160px;
}

/*********************************************************/
/************Mobile media query for Common style**********/
/*********************************************************/
@media screen and (min-width: 767px) and (max-width: 992px) {
  body {
    padding-top: 80px;
  }
  .header {
    .logo {
      max-width: 120px;
    }
    .btn {
      padding: 10px 18px;
    }
    .btn-secondary {
      margin-left: 10px;
    }
  }
  .menu li {
    padding: 0 12px;
    font-size: 15px;
  }
  .header .btn {
    font-size: 15px;
    padding: 8px 14px;
  }
}

@media screen and (min-width: 768px) {
  .mobile,
  .nav-trigger,
  .menu-wrapper .btn {
    display: none;
  }
}

@media screen and (max-width: 767px) {
  body {
    min-width: 290px;
    font-size: $font-small;
    padding: 80px 0 0 0;
  }

  h1 {
    font-size: 32px;
  }

  h2 {
    font-size: 28px;
  }

  h3 {
    font-size: 20px;
  }

  .btn {
    padding: 10px 22px;
    span {
      font-size: 16px;
    }
    i {
      width: 20px;
      height: 20px;
    }
  }

  .header {
    height: 80px;
    .logo {
      max-width: 120px;
    }
    .btn {
      display: none;
    }
  }

  .nav-trigger {
    position: relative;
    margin-left: auto;
    width: 40px;
    height: 40px;
    z-index: 10;
    background-color: $gray04;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    @include border-radius(50px);
    i {
      background: url(/assets/images/icon-hamburger.svg) no-repeat center center;
      width: 24px;
      height: 24px;
      @include transition(all);
      position: absolute;
    }

    .icon-close {
      background: url(/assets/images/icon-close.svg) no-repeat center center;
      opacity: 0;
    }
  }

  .menu-overlay {
    overflow: hidden;
    &::before {
      content: "";
      position: fixed;
      right: 0;
      top: 0;
      width: 100%;
      height: 100%;
      background: $secondary;
      opacity: 0;
      visibility: hidden;
      @include transition(all);
    }

    .menu-wrapper {
      position: fixed;
      right: 0;
      top: 0;
      background: $white;
      width: 0;
      height: 100%;
      max-width: 370px;
      @include transition(all);
      .btn {
        display: block;
        font-size: 16px;
        padding: 12px 20px;
        margin: 16px;
        white-space: nowrap;
      }
    }

    .menu {
      padding: 70px 0 0 0;
      li {
        display: block;
        padding: 0;
        margin: 0;
        font-size: 24px;

        &:after {
          width: 100%;
          height: 1px;
        }

        a {
          padding: 16px;
          display: block;
        }
      }
    }
  }

  .open {
    overflow: hidden;

    .icon-hamburger {
      opacity: 0;
    }
    .icon-close {
      opacity: 1;
    }
    .menu-overlay {
      z-index: 1;
      &::before {
        opacity: 0.6;
        visibility: visible;
      }
      .menu-wrapper {
        width: 80%;
      }
    }
  }
  .img-404 {
    margin: 30px auto 50px;
  }

  .single-page p,
  .single-page ul {
    font-size: 16px;
  }
}

.sms-section {
  padding: 60px 0;
}

.filter-section {
  padding: 30px 0;

  .row{
    display: flex;
    align-items: center;
  }

  div.custom-dropdown {
    position: relative;
    background-repeat: no-repeat;
    background-position: right center;
    background-image: url(/assets/images/triangle-image.png);

    >div {
      &.caption {
        padding: 6px 24px 6px 12px;
        border-radius: 3px;
        cursor: pointer;
        color: $primary;
        &::after{
          content: "";
          width: 1px;
          height: 1px;
          border-left: 6px solid transparent;
          border-right: 6px solid transparent;
          border-top: 6px solid $primary;
          margin-left: -10px;
          position: absolute;
          right: 0;
          top: 40%;
          z-index: 1;
        }
      }

      &.list {
        position: absolute;
        width: 230px;
        border-radius: 0 0 3px 3px;
        display: none;
        margin: 0 0 0 -10px;

        >div.item {
          padding: 11px 24px;
          cursor: pointer;
          width: 100%;
          transition: all 0.3s ease;

          &:hover {
            color: #000;
            background-color: $gray01;
          }

          &.selected {
            font-weight: bold;
          }
        }
      }
    }

    &.open>div {
      &.caption {
        border-radius: 3px 3px 0 0;
      }

      &.list {
        padding: 10px 0;
        display: block;
        background: #fff;
        border-radius: 10px 10px 10px 10px;
        z-index: 11;
        box-shadow: 0 8px 14px -6px #0000004f;
        overflow: hidden;
      }
    }
  }

  .left-part {
    display: flex;
    align-items: center;
  }

  h2 {
    padding-right: 20px;
    border-right: solid 1px var(--gray-lte);
    margin-bottom: 0;
    font-size: 16px;
    font-weight: 400;
  }

  .custom-dropdown {
    font-weight: 700;
    margin-left: 10px;

    &.open {
      background-color: #fff;
      width: auto;
      border-bottom: 0px;
      border-radius: 10px 10px 0 0;
      overflow: visible;
    }
  }

  .search-form {
    text-align: right;
    justify-content: right;
    margin: 0;
    padding: 0;

    .form-control {
      width: 220px;
      margin-right: 10px;
    }
  }

  @media screen and (max-width: 767px) {
    .row{
      flex-wrap: wrap;
    }
    .col-6{
      width: 100%;
    }
    .search-form{
      display: flex;
      flex-wrap: nowrap;
       .form-control{
        width: 100%;
       }
    }
  }
}

/* Animation Css */
.slide-animation {
  animation: fadezoom 8s 0s forwards;
}
@keyframes fadezoom {
  0% {
    transform: scale(1);
  }
  100% {
    transform: scale(1.1);
  }
}

@-webkit-keyframes fadeEffect {
  from {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}

@keyframes fadeEffect {
  from {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}

@keyframes animateContainer {
  0% {
    opacity: 0;
    transform: scale(0);
    box-shadow: 0px 0px 0px 65px rgba(255, 255, 255, 0.25) inset,
      0px 0px 0px 65px rgba(255, 255, 255, 0.25) inset;
  }
  25% {
    opacity: 1;
    transform: scale(0.9);
    box-shadow: 0px 0px 0px 65px rgba(255, 255, 255, 0.25) inset,
      0px 0px 0px 65px rgba(255, 255, 255, 0.25) inset;
  }
  43.75% {
    transform: scale(1.15);
    box-shadow: 0px 0px 0px 43.334px rgba(255, 255, 255, 0.25) inset,
      0px 0px 0px 65px rgba(255, 255, 255, 0.25) inset;
  }
  62.5% {
    transform: scale(1);
    box-shadow: 0px 0px 0px 0px rgba(255, 255, 255, 0.25) inset,
      0px 0px 0px 21.667px rgba(255, 255, 255, 0.25) inset;
  }
  81.25% {
    box-shadow: 0px 0px 0px 0px rgba(255, 255, 255, 0.25) inset,
      0px 0px 0px 0px rgba(255, 255, 255, 0.25) inset;
  }
  100% {
    opacity: 1;
    box-shadow: 0px 0px 0px 0px rgba(255, 255, 255, 0.25) inset,
      0px 0px 0px 0px rgba(255, 255, 255, 0.25) inset;
  }
}

@keyframes animateCheck {
  from {
    stroke-dashoffset: 80;
  }
  to {
    stroke-dashoffset: 0;
  }
}

@keyframes shadowRolling {
  0% {
    box-shadow: 0px 0 rgba($white, 0), 0px 0 rgba($white, 0),
      0px 0 rgba($white, 0), 0px 0 rgba($white, 0);
  }
  12% {
    box-shadow: 100px 0 rgba($white, 1), 0px 0 rgba($white, 0),
      0px 0 rgba($white, 0), 0px 0 rgba($white, 0);
  }
  25% {
    box-shadow: 110px 0 rgba($white, 1), 100px 0 rgba($white, 1),
      0px 0 rgba($white, 0), 0px 0 rgba($white, 0);
  }
  36% {
    box-shadow: 120px 0 rgba($white, 1), 110px 0 rgba($white, 1),
      100px 0 rgba($white, 1), 0px 0 rgba($white, 0);
  }
  50% {
    box-shadow: 130px 0 rgba($white, 1), 120px 0 rgba($white, 1),
      110px 0 rgba($white, 1), 100px 0 rgba($white, 1);
  }
  62% {
    box-shadow: 200px 0 rgba($white, 0), 130px 0 rgba($white, 1),
      120px 0 rgba($white, 1), 110px 0 rgba($white, 1);
  }
  75% {
    box-shadow: 200px 0 rgba($white, 0), 200px 0 rgba($white, 0),
      130px 0 rgba($white, 1), 120px 0 rgba($white, 1);
  }
  87% {
    box-shadow: 200px 0 rgba($white, 0), 200px 0 rgba($white, 0),
      200px 0 rgba($white, 0), 130px 0 rgba($white, 1);
  }
  100% {
    box-shadow: 200px 0 rgba($white, 0), 200px 0 rgba($white, 0),
      200px 0 rgba($white, 0), 200px 0 rgba($white, 0);
  }
}
