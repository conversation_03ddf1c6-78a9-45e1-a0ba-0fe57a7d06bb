/* Portfolio: Banner section ***********/
.banner-section {
  background-color: $secondary;
  position: relative;
  min-height: 300px;
  overflow: hidden;
  display: flex;
  align-items: center;
  &::after {
    content: "";
    @include position;
    background: linear-gradient(90deg, #000000 -0.47%, rgba(0, 0, 0, 0) 83.85%);
  }
  img {
    position: absolute;
    left: 0;
    top: 0;
    object-fit: cover;
    height: 100%;
    width: 100%;
  }
  .container {
    position: relative;
    z-index: 1;
    width: 100%;
    h1 {
      color: $white;
      text-transform: uppercase;
      margin: 15px 0;
    }
  }
  @media screen and (max-width: 767px) {
    min-height: 200px;
  }
}

.portfolio-section {
  .title {
    text-align: left;
    border-left: solid 1px $primary;
    padding: 60px 60px 10px;
    max-width: 815px;
  }
  .card {
    width: 100%;
    margin: 8px 12px;
    .card-img {
      height: 320px;
    }
  }
  @media screen and (max-width: 767px) {
    .title {
      padding: 20px 20px 0;
    }
  }
}

/* Portfolio: Global section ***********/
.global-portfolio-section {
  padding: 0 0 80px 0;
  .container {
    background: url(/assets/images/img-bg.svg) no-repeat 0 center;
    background-size: auto 88%;
  }
  .db-img {
    width: 44%;
    padding-left: 20px;
    img {
      box-shadow: -4px 0px 76px rgba(0, 0, 0, 0.16);
    }
  }
  .db-info {
    width: 56%;
    padding: 40px;
    p {
      font-size: 18px;
      color: $gray03;
    }
  }
  @media screen and (max-width: 767px) {
    padding: 30px 0;
    .container {
      background-position: -100px center;
      background-size: auto 74%;
      .db-img {
        width: 100%;
        padding: 0;
      }
      .db-info {
        width: 100%;
        padding: 30px 0;
        p {
          font-size: 16px;
        }
      }
    }
  }
}

/* Portfolio: Awards carousel section ***********/
.awards-portfolio-carousel {
  padding-bottom: 50px;
  .awards-item {
    border: 1px solid $gray;
    padding: 20px;
    margin: 12px;
    width: calc(100% - 24px) !important;
    text-align: center;
    min-height: 180px;
    @include transition(all);
    .carousel-logo {
      height: 82px;
      display: flex;
      align-items: center;
      img {
        margin: 0 auto;
        @include grayscale;
      }
    }
    &:hover {
      border-color: $gray02;
      img {
        filter: grayscale(0);
        -webkit-filter: grayscale(0);
      }
    }
    h4 {
      font-size: 14px;
      color: $secondary;
      margin-bottom: 5px;
    }
    p {
      font-size: 12px;
      color: $gray02;
      margin: 0;
    }
  }
}
