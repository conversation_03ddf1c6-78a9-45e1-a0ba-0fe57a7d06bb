@mixin common {
  font-family: $font-family;
  font-size: $font-size;
  font-weight: $font-weight;
  line-height: $line-height;
  color: $gray03;
}

@mixin transition($property) {
  transition-property: $property;
  transition-duration: 0.3s;
  transition-timing-function: ease-in-out;
}

@mixin border-box {
  -moz-box-sizing: border-box;
  -webkit-box-sizing: border-box;
  -ms-box-sizing: border-box;
  -o-box-sizing: border-box;
  box-sizing: border-box;
  outline: none;
}

@mixin border-radius($radius) {
  -webkit-border-radius: ($radius);
  -moz-border-radius: ($radius);
  -ms-border-radius: ($radius);
  -o-border-radius: ($radius);
  border-radius: ($radius);
}

@mixin scale($scale) {
  -webkit-transform: scale($scale);
  transform: scale($scale, $scale);
  -ms-transform: scale($scale, $scale); /* IE 9 */
  -webkit-transform: scale($scale, $scale); /* Safari and Chrome */
}

@mixin rotate($rotate) {
  -moz-transform: rotate($rotate);
  -ms-transform: rotate($rotate);
  -webkit-transform: rotate($rotate);
  transform: rotate($rotate);
}

@mixin background-image($image-url) {
  background-image: url($image-url);
  background-repeat: no-repeat;
  background-size: cover;
  background-position: center center;
}

@mixin set-font-size($factor) {
  font-size: round($font-size * $factor);
  line-height: round($font-size * $line-height-factor * $factor);
}

@mixin no-list {
  list-style: none;
  margin: 0;
  padding: 0;
}

@mixin initial {
  padding: 0;
  margin: 0;
  height: initial;
  width: initial;
  display: none;
  cursor: pointer;
}

@mixin btn {
  font-family: $font-family-primary;
  font-size: $font-size;
  color: $white;
  padding: 13px 26px;
  font-weight: $font-weight;
  letter-spacing: 0.04em;
  text-transform: uppercase;
  text-align: center;
  display: inline-block;
  position: relative;
  text-decoration: none;
  border: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  border-radius: 0;
  @include transition(all);
  cursor: pointer;
}

@mixin link {
  text-decoration: none;
  position: relative;
  background: linear-gradient(180deg, currentColor 1px, transparent 0);
  background-repeat: no-repeat;
  background-position: 100% 100%;
  background-size: 0 1px;
  transition: background-size 0.3s;
  cursor: pointer;
  padding-bottom: 1px;
}

@mixin link-hover {
  background-image: linear-gradient(180deg, currentColor 1px, transparent 0);
  background-size: 100% 1px;
  background-position: 0 100%;
}

@mixin grayscale {
  filter: grayscale(100%);
  -webkit-filter: grayscale(100%);
  transition: filter 600ms ease;
  -webkit-transition: -webkit-filter 600ms ease;
}

@mixin input {
  font-size: $font-size;
  font-family: $font-family;
  font-weight: $font-weight;
  padding: 10px 15px;
  border: none;
  display: block;
  background-color: $white;
  @include shadow;
}

@mixin select {
  font-size: $font-size;
  border: solid 1px $gray;
  background-color: $white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  height: 30px;
  min-width: 80px;
  position: relative;
  display: flex;
  overflow: hidden;
  padding-left: 10px;
}

@mixin shadow {
  box-shadow: 0px 4px 44px rgba(0, 0, 0, 0.1);
}

@mixin col {
  float: left;
  padding-left: 15px;
  padding-right: 15px;
  position: relative;
}

@mixin position {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
}
