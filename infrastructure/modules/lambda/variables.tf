variable "authorization_type" {
  description = "Lambda function url access authorization type"
  type        = string
  default     = "NONE"
}

variable "env_vars_from_parameter_store" {
  description = "Lambda environment variables from SSM parameter store"
  type        = map(string)
  default     = {}
}

variable "function_url" {
  description = "Create lambda function url"
  type        = bool
  default     = false
}

variable "function_url_cors" {
  description = "Function url cors configuration"
  type = object({
    allow_credentials = optional(bool, false)
    allow_headers     = optional(list(string), ["accept", "content-type"])
    allow_methods     = optional(list(string), ["GET", "POST"])
    allow_origins     = optional(list(string), ["*"])
    expose_headers    = optional(list(string), [])
    max_age          = optional(number, 86400)
  })
  default = {}
}

variable "description" {
  description = "Lambda function description"
}

variable "environment_variables" {
  type        = map(any)
  description = "Environment Variables for Lambda Functions"
  default     = {}
}

variable "function_name" {
  description = "Lambda function name"
}

variable "handler" {
  description = "Name of Handler"
}

variable "lambda_artifacts_bucket" {
  description = "Lambda artifacts bucket"
}

variable "lambda_memory" {
  description = "Required Memory for Lambda function"
  default     = 128
}

variable "lambda_runtime" {
  description = "Lambda language"
}

variable "layers_arn" {
  description = "Lambda layer arn"
  type        = list(string)
  default     = null
}

variable "lambda_timeout" {
  description = "Required Timeout for Lambda function"
  default     = 5
}

variable "publish" {
  description = "Publish lambda function version"
  type        = bool
  default     = false
}

variable "source_code" {
  description = "Lambda source code S3 key"
  type        = string
}
