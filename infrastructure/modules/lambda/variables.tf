variable "apigw_execution_arn" {
  description = "Apigw execution arn"
  default     = []
}

variable "authorization_type" {
  description = "Lambda function url access authorization type"
  default     = "NONE"
}

variable "env_vars_from_parameter_store" {
  description = "Lambda environment variables from SSM parameter store"
  type        = map(any)
  default     = {}
}

variable "function_url" {
  description = "Create lambda function url"
  type        = bool
  default     = false
}

variable "function_url_cors" {
  description = "Function url cors"
  type        = any
  default     = {}
}

variable "description" {
  description = "Lambda function description"
}

variable "environment_variables" {
  type        = map(any)
  description = "Environment Variables for Lambda Functions"
  default     = {}
}

variable "function_name" {
  description = "Lambda function name"
}

variable "handler" {
  description = "Name of Handler"
}

variable "lambda_artifacts_bucket" {
  description = "Lambda artifacts bucket"
}

variable "lambda_memory" {
  description = "Required Memory for Lambda function"
  default     = 128
}

variable "lambda_runtime" {
  description = "Lambda language"
}

variable "layers_arn" {
  description = "Lambda layer arn"
  type        = list(string)
  default     = null
}

variable "lambda_timeout" {
  description = "Required Timeout for Lambda function"
  default     = 5
}

variable "publish" {
  description = "Publish lambda function version"
  default     = false
}

variable "security_group_ids" {
  description = "Security geoup id"
  type        = list(any)
  default     = null
}

variable "source_code" {
  description = "Lambda source code"
}

variable "subnets" {
  description = "Subnets"
  type        = list(any)
  default     = null
}
