data "aws_s3_object" "lambda" {
  bucket = var.lambda_artifacts_bucket
  key    = var.source_code
}

data "aws_ssm_parameter" "env_vars_from_parameter_store" {
  for_each = var.env_vars_from_parameter_store
  name     = each.value
}

locals {
  env_vars_from_parameter_store = length(keys(var.env_vars_from_parameter_store)) == 0 ? {} : zipmap(
    [for key, value in var.env_vars_from_parameter_store : key],
    [for parameter in data.aws_ssm_parameter.env_vars_from_parameter_store : parameter.value]
  )
  environment_variables = length(keys(var.environment_variables)) == 0 && length(keys(local.env_vars_from_parameter_store)) == 0 ? [] : [merge(var.environment_variables, local.env_vars_from_parameter_store)]
}

resource "aws_lambda_function" "lambda" {
  function_name     = var.function_name
  description       = var.description
  handler           = var.handler
  memory_size       = var.lambda_memory
  role              = aws_iam_role.lambda.arn
  runtime           = var.lambda_runtime
  timeout           = var.lambda_timeout
  s3_bucket         = data.aws_s3_object.lambda.bucket
  s3_key            = data.aws_s3_object.lambda.key
  s3_object_version = data.aws_s3_object.lambda.version_id
  layers            = var.layers_arn
  publish           = var.publish

  dynamic "environment" {
    for_each = length(local.environment_variables) == 0 ? [] : local.environment_variables
    content {
      variables = environment.value
    }
  }

  dynamic "vpc_config" {
    for_each = var.subnets != null && var.security_group_ids != null ? [true] : []
    content {
      security_group_ids = var.security_group_ids
      subnet_ids         = var.subnets
    }
  }
}

resource "aws_lambda_permission" "api" {
  count         = length(var.apigw_execution_arn) > 0 ? 1 : 0
  statement_id  = "AllowAPIGWLambdaInvoke"
  action        = "lambda:InvokeFunction"
  function_name = aws_lambda_function.lambda.function_name
  principal     = "apigateway.amazonaws.com"
  source_arn    = "${var.apigw_execution_arn}/*/*/*"
}

resource "aws_lambda_function_url" "function_url" {
  count              = var.function_url ? 1 : 0
  function_name      = aws_lambda_function.lambda.function_name
  authorization_type = var.authorization_type

  dynamic "cors" {
    for_each = length(keys(var.function_url_cors)) == 0 ? [] : [var.function_url_cors]

    content {
      allow_credentials = try(cors.value.allow_credentials, null)
      allow_headers     = try(cors.value.allow_headers, null)
      allow_methods     = try(cors.value.allow_methods, null)
      allow_origins     = try(cors.value.allow_origins, null)
      expose_headers    = try(cors.value.expose_headers, null)
      max_age           = try(cors.value.max_age, null)
    }
  }
}

resource "aws_cloudwatch_log_group" "lambda" {
  name              = "/aws/lambda/${aws_lambda_function.lambda.function_name}"
  retention_in_days = length(regexall("-prod-", var.function_name)) > 0 ? 180 : 90
}
