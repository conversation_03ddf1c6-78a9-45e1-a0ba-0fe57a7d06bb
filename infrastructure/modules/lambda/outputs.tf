output "arn" {
  value = aws_lambda_function.lambda.arn
}

output "function_name" {
  value = aws_lambda_function.lambda.function_name
}

output "function_url" {
  value = var.function_url ? aws_lambda_function_url.function_url[0].function_url : null
}

output "function_endpoint" {
  value = var.function_url ? replace(replace(aws_lambda_function_url.function_url[0].function_url, "https://", ""), "/", "") : null
}

output "role_name" {
  value = aws_iam_role.lambda.name
}

output "invoke_arn" {
  value = aws_lambda_function.lambda.invoke_arn
}
