resource "aws_iam_user" "iam_user" {
  name = var.name
  lifecycle {
    prevent_destroy = true
  }
}

resource "aws_iam_user_policy" "iam_user_policy" {
  name   = var.name
  user   = aws_iam_user.iam_user.name
  policy = <<EOF
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Sid": "Statement1",
            "Effect": "Allow",
            "Action": [
                "s3:GetObject"
            ],
            "Resource": [                 
                "${var.bucket_arn}/*"
            ]
        }
    ]
}
EOF
}

resource "aws_iam_access_key" "iam_user_access_key" {
  user = aws_iam_user.iam_user.name
  lifecycle {
    prevent_destroy = true
  }
}