#------------------------------------------------------------------------------------
# Data resources to fetch values
#------------------------------------------------------------------------------------
data "aws_region" "current" {}

#------------------------------------------------------------------------------------
# Domain Identity
#------------------------------------------------------------------------------------
resource "aws_ses_domain_identity" "domain" {
  domain = var.domain
  lifecycle {
    prevent_destroy = true
  }
}

#------------------------------------------------------------------------------------
# Domain DKIM
#------------------------------------------------------------------------------------
resource "aws_ses_domain_dkim" "domain" {
  domain = aws_ses_domain_identity.domain.domain
  lifecycle {
    prevent_destroy = true
  }
}

#------------------------------------------------------------------------------------
# Outputs
#------------------------------------------------------------------------------------
output "domain" {
  value = aws_ses_domain_identity.domain.domain
}

output "arn" {
  value = aws_ses_domain_identity.domain.arn
}

#------------------------------------------------------------------------------------
# Variables
#------------------------------------------------------------------------------------
variable "domain" {
  description = "Domain name"
  type        = string
}
