variable "acm_arn" {
  description = "ACM cert arn"
  type        = string
  default     = ""
}

variable "allowed_methods" {
  description = "Allowed methods"
  type        = list(any)
  default     = ["GET", "HEAD"]
}

variable "cached_methods" {
  description = "Cached methods"
  type        = list(any)
  default     = ["GET", "HEAD"]
}

variable "custom_error_response" {
  description = "Custom error response"
  type        = list(any)
  default     = []
}

variable "comment" {
  description = "Comment for cloudfront distribution"
  type        = string
  default     = null
}

variable "default_root_object" {
  description = "Default root object"
  type        = string
  default     = ""
}

variable "domain_aliases" {
  description = "domain aliases"
  type        = list(string)
  default     = null
}

variable "enabled" {
  description = "Cloudfront state"
  type        = bool
  default     = true
}

variable "function_association" {
  description = "Function association"
  type        = list(any)
  default     = []
}

variable "lambda_function_association" {
  description = "Lambda edge association"
  type        = list(any)
  default     = []
}

variable "logging_config" {
  description = "Cloudfront logging config"
  type        = map(any)
  default     = {}
}

variable "origin" {
  description = "Origin configuration"
  type        = any
}

variable "route53_zone_id" {
  description = "Route53 zone id"
  type        = string
  default     = ""
}

variable "response_headers_policy_id" {
  description = "Response header policy id"
  default     = null
}

variable "ttl_values" {
  description = "map of ttl variables"
  type        = map(any)
  default     = {}
}

variable "web_acl_id" {
  description = "WAF web ACL id"
  type        = string
  default     = ""
}

variable "prefix" {
  description = "Name prefix value"
  default     = ""
}

variable "function_name" {
  description = "Function name"
  default     = ""
}

variable "create_oac" {
  description = "Create CF OAC"
  default     = false
}

variable "create_lambda_permission" {
  description = "Create Lambda permission"
  default     = false
}

variable "origin_request_policy_id" {
  description = "Origin request policy id"
  default     = ""
}

variable "cache_policy_id" {
  description = "Cache policy id"
  default     = ""
}

variable "cloudfront_default_certificate" {
  description = "Cloudfront default certification"
  default     = false
}