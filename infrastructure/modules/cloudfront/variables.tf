variable "acm_arn" {
  description = "ACM certificate ARN for SSL/TLS"
  type        = string
  default     = null
}

variable "allowed_methods" {
  description = "HTTP methods allowed by CloudFront"
  type        = list(string)
  default     = ["GET", "HEAD", "OPTIONS", "PUT", "POST", "PATCH", "DELETE"]
}

variable "cached_methods" {
  description = "HTTP methods cached by CloudFront"
  type        = list(string)
  default     = ["GET", "HEAD"]
}

variable "custom_error_response" {
  description = "Custom error response configuration"
  type = list(object({
    error_code            = number
    error_caching_min_ttl = optional(number, 300)
    response_code         = optional(number)
    response_page_path    = optional(string)
  }))
  default = []
}

variable "comment" {
  description = "Comment for CloudFront distribution"
  type        = string
  default     = "URW Airports Website Distribution"
}

variable "default_root_object" {
  description = "Default root object"
  type        = string
  default     = "index.html"
}

variable "domain_aliases" {
  description = "Domain aliases for the distribution"
  type        = list(string)
  default     = []
}

variable "enabled" {
  description = "Whether the distribution is enabled"
  type        = bool
  default     = true
}

variable "function_association" {
  description = "CloudFront function associations"
  type = list(object({
    event_type   = string
    function_arn = string
  }))
  default = []
}

variable "origin" {
  description = "Origin configuration"
  type = object({
    domain_name = string
    origin_id   = string
    custom_origin_config = optional(object({
      origin_protocol_policy = string
      http_port             = optional(number, 80)
      https_port            = optional(number, 443)
      origin_ssl_protocols  = optional(list(string), ["TLSv1.2"])
    }))
    s3_origin_config = optional(object({
      s3_origin_access_identity = string
    }))
    custom_header = optional(list(object({
      name  = string
      value = string
    })), [])
  })
}

variable "response_headers_policy_id" {
  description = "Response headers policy ID"
  type        = string
  default     = null
}

variable "ttl_values" {
  description = "TTL configuration"
  type = object({
    min_ttl     = optional(number, 0)
    default_ttl = optional(number, 86400)
    max_ttl     = optional(number, 31536000)
  })
  default = {}
}

variable "web_acl_id" {
  description = "WAF web ACL ID (optional)"
  type        = string
  default     = null
}

variable "function_name" {
  description = "Lambda function name for OAC permissions"
  type        = string
  default     = ""
}

variable "create_oac" {
  description = "Create CloudFront Origin Access Control"
  type        = bool
  default     = false
}

variable "create_lambda_permission" {
  description = "Create Lambda permission for CloudFront"
  type        = bool
  default     = false
}

variable "origin_request_policy_id" {
  description = "Origin request policy ID"
  type        = string
  default     = null
}

variable "cache_policy_id" {
  description = "Cache policy ID"
  type        = string
  default     = null
}

variable "cloudfront_default_certificate" {
  description = "Use CloudFront default certificate"
  type        = bool
  default     = false
}