#tfsec:ignore:aws-cloudfront-enable-logging
resource "aws_cloudfront_distribution" "cloudfront" {
  enabled    = var.enabled
  web_acl_id = var.web_acl_id #tfsec:ignore:aws-cloudfront-enable-waf
  comment    = var.comment

  dynamic "origin" {
    for_each = [var.origin]

    content {
      domain_name              = origin.value.domain_name
      origin_id                = origin.value.origin_id
      origin_access_control_id = var.create_oac ? aws_cloudfront_origin_access_control.lambda[0].id : null

      dynamic "s3_origin_config" {
        for_each = length(keys(lookup(origin.value, "s3_origin_config", {}))) > 0 ? [lookup(origin.value, "s3_origin_config")] : []
        content {
          origin_access_identity = s3_origin_config.value.s3_origin_access_identity
        }
      }

      dynamic "custom_origin_config" {
        for_each = length(lookup(origin.value, "custom_origin_config", "")) > 0 ? [lookup(origin.value, "custom_origin_config")] : []
        content {
          origin_protocol_policy = custom_origin_config.value.origin_protocol_policy
          http_port              = lookup(custom_origin_config.value, "http_port", 80)
          https_port             = lookup(custom_origin_config.value, "https_port", 443)
          origin_ssl_protocols   = lookup(custom_origin_config.value, "origin_ssl_protocols", ["TLSv1", "TLSv1.1", "TLSv1.2"])
        }
      }

      dynamic "custom_header" {
        for_each = lookup(origin.value, "custom_header", [])
        content {
          name  = custom_header.value.name
          value = custom_header.value.value
        }
      }
    }
  }

  viewer_certificate {
    acm_certificate_arn            = var.cloudfront_default_certificate ? null : var.acm_arn
    ssl_support_method             = var.cloudfront_default_certificate ? null : "sni-only"
    minimum_protocol_version       = var.cloudfront_default_certificate ? null : "TLSv1.2_2021"
    cloudfront_default_certificate = var.cloudfront_default_certificate ? true : false
  }

  aliases             = var.domain_aliases
  default_root_object = var.default_root_object

  default_cache_behavior {
    allowed_methods  = var.allowed_methods
    cached_methods   = var.cached_methods
    target_origin_id = var.origin["origin_id"]

    viewer_protocol_policy = "redirect-to-https"
    compress               = true

    min_ttl     = lookup(var.ttl_values, "min_ttl", null)
    max_ttl     = lookup(var.ttl_values, "max_ttl", null)
    default_ttl = lookup(var.ttl_values, "default_ttl", null)

    cache_policy_id            = var.cache_policy_id
    origin_request_policy_id   = var.origin_request_policy_id
    response_headers_policy_id = var.response_headers_policy_id

    dynamic "forwarded_values" {
      for_each = var.cache_policy_id != "" ? [] : [1]

      content {
        query_string = false

        cookies {
          forward = "none"
        }
      }
    }

    dynamic "lambda_function_association" {
      for_each = var.lambda_function_association
      content {
        event_type   = lambda_function_association.value.event_type
        lambda_arn   = lambda_function_association.value.lambda_arn
        include_body = lambda_function_association.value.include_body
      }
    }

    dynamic "function_association" {
      for_each = var.function_association
      content {
        event_type   = function_association.value.event_type
        function_arn = function_association.value.function_arn
      }
    }
  }

  dynamic "custom_error_response" {
    for_each = var.custom_error_response
    content {
      error_code            = custom_error_response.value.error_code
      error_caching_min_ttl = custom_error_response.value.error_caching_min_ttl
      response_code         = try(custom_error_response.value.response_code, null)
      response_page_path    = try(custom_error_response.value.response_page_path, null)
    }
  }

  restrictions {
    geo_restriction {
      restriction_type = "none"
    }
  }

  dynamic "logging_config" {
    for_each = length(keys(var.logging_config)) > 0 ? [var.logging_config] : []
    content {
      bucket          = logging_config.value.bucket
      prefix          = logging_config.value.prefix
      include_cookies = logging_config.value.include_cookies
    }
  }

  lifecycle {
    prevent_destroy = true
  }
}

resource "aws_cloudfront_origin_access_control" "lambda" {
  count                             = var.create_oac ? 1 : 0
  name                              = "${var.function_name}-lambda"
  origin_access_control_origin_type = "lambda"
  signing_behavior                  = "always"
  signing_protocol                  = "sigv4"
}

resource "aws_lambda_permission" "allow_cloudfront" {
  count         = var.create_lambda_permission ? 1 : 0
  statement_id  = "AllowExecutionFromCloudFront"
  action        = "lambda:InvokeFunctionUrl"
  function_name = var.function_name
  principal     = "cloudfront.amazonaws.com"
  source_arn    = aws_cloudfront_distribution.cloudfront.arn
  provider      = aws.us-west-2
}