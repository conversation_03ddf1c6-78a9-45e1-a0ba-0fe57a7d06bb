variable "name" {
  description = "S3 bucket name"
  type        = string
}

variable "force_destroy" {
  description = "Allow bucket to be destroyed even if it contains objects"
  type        = bool
  default     = false
}

variable "versioning_enabled" {
  description = "Enable versioning for the S3 bucket"
  type        = bool
  default     = false
}

variable "block_public_access" {
  description = "Block all public access to the bucket"
  type        = bool
  default     = true
}

variable "cloudfront_access_enabled" {
  description = "Enable CloudFront Origin Access Identity for the bucket"
  type        = bool
  default     = false
}

variable "public_read_access" {
  description = "Allow public read access to objects (for website hosting)"
  type        = bool
  default     = false
}

variable "website_enabled" {
  description = "Enable static website hosting"
  type        = bool
  default     = false
}

variable "index_document" {
  description = "Index document for website hosting"
  type        = string
  default     = "index.html"
}

variable "error_document" {
  description = "Error document for website hosting"
  type        = string
  default     = "error.html"
}

variable "redirect_all_requests_to" {
  description = "Redirect all requests to another host"
  type = object({
    host_name = string
    protocol  = optional(string, "https")
  })
  default = null
}
