# S3 Bucket
resource "aws_s3_bucket" "s3" {
  bucket        = var.name
  force_destroy = var.force_destroy

  lifecycle {
    prevent_destroy = true
  }
}

# Server-side encryption
resource "aws_s3_bucket_server_side_encryption_configuration" "encryption" {
  bucket = aws_s3_bucket.s3.bucket

  rule {
    apply_server_side_encryption_by_default {
      sse_algorithm = "AES256"
    }
  }
}

# Versioning
resource "aws_s3_bucket_versioning" "versioning" {
  count  = var.versioning_enabled ? 1 : 0
  bucket = aws_s3_bucket.s3.id
  versioning_configuration {
    status = "Enabled"
  }
}

# Public access block
resource "aws_s3_bucket_public_access_block" "public_access_block" {
  count  = var.block_public_access ? 1 : 0
  bucket = aws_s3_bucket.s3.id

  block_public_acls       = true
  block_public_policy     = true
  ignore_public_acls      = true
  restrict_public_buckets = true
}

# Website configuration
resource "aws_s3_bucket_website_configuration" "website" {
  count  = var.website_enabled ? 1 : 0
  bucket = aws_s3_bucket.s3.bucket

  index_document {
    suffix = var.index_document
  }

  error_document {
    key = var.error_document
  }

  dynamic "redirect_all_requests_to" {
    for_each = var.redirect_all_requests_to != null ? [var.redirect_all_requests_to] : []
    content {
      host_name = redirect_all_requests_to.value.host_name
      protocol  = redirect_all_requests_to.value.protocol
    }
  }
}

# CloudFront Origin Access Identity
resource "aws_cloudfront_origin_access_identity" "cloudfront" {
  count   = var.cloudfront_access_enabled ? 1 : 0
  comment = "Origin Access Identity for ${var.name}"
}

# S3 bucket policy for CloudFront access
resource "aws_s3_bucket_policy" "cloudfront_access" {
  count  = var.cloudfront_access_enabled ? 1 : 0
  bucket = aws_s3_bucket.s3.id
  policy = data.aws_iam_policy_document.cloudfront_access[0].json
}

data "aws_iam_policy_document" "cloudfront_access" {
  count = var.cloudfront_access_enabled ? 1 : 0

  statement {
    sid    = "AllowCloudfrontToListBucket"
    effect = "Allow"

    principals {
      type        = "AWS"
      identifiers = [aws_cloudfront_origin_access_identity.cloudfront[0].iam_arn]
    }

    actions   = ["s3:ListBucket"]
    resources = [aws_s3_bucket.s3.arn]
  }

  statement {
    sid    = "AllowCloudfrontToGetObjects"
    effect = "Allow"

    principals {
      type        = "AWS"
      identifiers = [aws_cloudfront_origin_access_identity.cloudfront[0].iam_arn]
    }

    actions   = ["s3:GetObject"]
    resources = ["${aws_s3_bucket.s3.arn}/*"]
  }
}

# Public read access policy (for website hosting)
resource "aws_s3_bucket_policy" "public_read" {
  count  = var.public_read_access ? 1 : 0
  bucket = aws_s3_bucket.s3.id
  policy = data.aws_iam_policy_document.public_read[0].json
}

data "aws_iam_policy_document" "public_read" {
  count = var.public_read_access ? 1 : 0

  statement {
    sid    = "AllowPublicReadAccess"
    effect = "Allow"

    principals {
      type        = "*"
      identifiers = ["*"]
    }

    actions   = ["s3:GetObject"]
    resources = ["${aws_s3_bucket.s3.arn}/*"]
  }
}
