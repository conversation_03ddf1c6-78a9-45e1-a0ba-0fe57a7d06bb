#tfsec:ignore:aws-s3-block-public-acls tfsec:ignore:aws-s3-block-public-policy tfsec:ignore:aws-s3-ignore-public-acls tfsec:ignore:aws-s3-no-public-buckets tfsec:ignore:aws-s3-enable-bucket-logging tfsec:ignore:aws-s3-specify-public-access-block
resource "aws_s3_bucket" "s3" {
  bucket        = var.name
  force_destroy = var.force_destroy

  lifecycle {
    prevent_destroy = true
    ignore_changes = [
      website
    ]
  }
}

## encryption
#tfsec:ignore:aws-s3-encryption-customer-key
resource "aws_s3_bucket_server_side_encryption_configuration" "encryption" {
  bucket = aws_s3_bucket.s3.bucket

  rule {
    apply_server_side_encryption_by_default {
      sse_algorithm = "AES256"
    }
  }
}

## versioning
#tfsec:ignore:aws-s3-enable-versioning
resource "aws_s3_bucket_versioning" "versioning" {
  count  = var.versioning == "Enabled" ? 1 : 0
  bucket = aws_s3_bucket.s3.id
  versioning_configuration {
    status = var.versioning
  }
}

## ACL
# resource "aws_s3_bucket_acl" "acl" {
#   bucket = aws_s3_bucket.s3.id
#   acl    = var.acl
# }

resource "aws_s3_bucket_public_access_block" "example" {
  count  = var.public_access_block ? 1 : 0
  bucket = aws_s3_bucket.s3.id

  block_public_acls       = false
  block_public_policy     = false
  ignore_public_acls      = false
  restrict_public_buckets = false
}
