output "arn" {
  value = aws_s3_bucket.s3.arn
}

output "regional_domain_name" {
  value = aws_s3_bucket.s3.bucket_regional_domain_name
}

output "cloudfront_access_identity_path" {
  value = var.cloudfront_access_policy ? aws_cloudfront_origin_access_identity.cloudfront[0].cloudfront_access_identity_path : null
}

output "id" {
  value = aws_s3_bucket.s3.id
}

output "website_endpoint" {
  value = length(keys(var.website)) > 0 ? aws_s3_bucket_website_configuration.website[0].website_endpoint : null
}
