variable "attributes" {
  description = "List of attribute definitions for the table"
  type = list(object({
    name = string
    type = string # S, N, or B for String, Number, or Binary
  }))
  default = []
}

variable "billing_mode" {
  description = "DynamoDB billing mode (PROVISIONED or PAY_PER_REQUEST)"
  type        = string
  default     = "PAY_PER_REQUEST"

  validation {
    condition     = contains(["PROVISIONED", "PAY_PER_REQUEST"], var.billing_mode)
    error_message = "Billing mode must be either PROVISIONED or PAY_PER_REQUEST."
  }
}

variable "deletion_protection" {
  description = "Enable deletion protection for the table"
  type        = bool
  default     = true
}

variable "global_secondary_indexes" {
  description = "Global secondary indexes configuration"
  type = list(object({
    name            = string
    hash_key        = string
    range_key       = optional(string)
    projection_type = string
  }))
  default = []
}

variable "hash_key" {
  description = "Primary hash key (partition key) for the table"
  type        = string
}

variable "name" {
  description = "Name of the DynamoDB table"
  type        = string
}

variable "range_key" {
  description = "Primary range key (sort key) for the table"
  type        = string
  default     = null
}
