variable "attributes" {
  description = "List of nested attribute definitions. Only required for hash_key and range_key attributes. Each attribute has two properties: name - (Required) The name of the attribute, type - (Required) Attribute type, which must be a scalar type: S, N, or B for (S)tring, (N)umber or (B)inary data"
  type        = list(map(string))
  default     = []
}

variable "billing_mode" {
  description = "Billing mode"
  type        = string
}

variable "deletion_protection" {
  description = "Deletion protection"
  type        = bool
  default     = true
}

variable "global_secondary_indexes" {
  description = "Global secondary indexes"
  type        = any
  default     = []
}

variable "hash_key" {
  description = "Hash key"
  type        = string
}

variable "name" {
  description = "Table name"
  type        = string
}

variable "range_key" {
  description = "Range key"
  type        = string
}
