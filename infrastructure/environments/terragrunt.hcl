## Required terraform and terragrunt version
terraform_version_constraint  = ">= 1.2.7"
terragrunt_version_constraint = ">= 0.38.7"

locals {
  # Read configuration files
  regionvars = read_terragrunt_config(find_in_parent_folders("region.hcl"))
  globalvars = read_terragrunt_config(find_in_parent_folders("global.hcl"))
  envvars    = read_terragrunt_config(find_in_parent_folders("env.hcl"))

  # Extract values
  service_region   = local.regionvars.locals.aws_region
  repo_name        = local.globalvars.locals.repo_name
  aws_profile      = local.envvars.locals.aws_profile
  prefix           = local.envvars.locals.prefix
  tf_bucket        = local.envvars.locals.tf_bucket
  env              = local.envvars.locals.env
  tf_bucket_region = get_env("TF_aws_region", "us-west-2")
}

## AWS Provider Config
generate "provider" {
  path      = "provider.tf"
  if_exists = "overwrite"
  contents  = <<EOF
provider "aws" {
  profile = "${local.aws_profile}"
  region = "${local.service_region}"
  default_tags {
    tags = {
      Environment = "${local.env}"
      Owner       = "Terraform"
      RepoName    = "${local.repo_name}"
    }
  }
}
EOF
}

## Terraform Remote state config
remote_state {
  backend = "s3"
  config = {
    encrypt        = true
    bucket         = local.tf_bucket
    key            = "${local.repo_name}/${path_relative_to_include()}/terraform.tfstate"
    region         = local.tf_bucket_region
    profile        = local.aws_profile
    dynamodb_table = "${local.prefix}-tfstate-lock"
  }
  generate = {
    path      = "backend.tf"
    if_exists = "overwrite"
  }
}
