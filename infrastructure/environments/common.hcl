# Common configuration shared across all environments
locals {
  # Common resource configurations
  lambda_runtime = "nodejs22.x"
  lambda_timeout = 30
  
  # Common domain and email settings
  ses_domain_identity = "e.urwairports.com"
  source_email       = "<EMAIL>"
  
  # Common tags that will be applied to all resources
  common_tags = {
    Project     = "URW-Airports"
    ManagedBy   = "Terraform"
    Repository  = "urw-airports-website"
  }
  
  # Lambda layer configuration
  lambda_layer_config = {
    runtime = "nodejs22.x"
    description = "Common Node.js dependencies for URW Airports lambdas"
  }
  
  # CloudFront configuration
  cloudfront_config = {
    price_class = "PriceClass_100"  # Use only North America and Europe edge locations
    ttl_values = {
      min_ttl     = 0
      max_ttl     = 31536000  # 1 year
      default_ttl = 604800    # 1 week
    }
    custom_error_responses = [
      {
        error_code            = 404
        error_caching_min_ttl = 600
      }
    ]
  }
  
  # DynamoDB configuration
  dynamodb_config = {
    billing_mode = "PAY_PER_REQUEST"
    deletion_protection = true
  }
}
