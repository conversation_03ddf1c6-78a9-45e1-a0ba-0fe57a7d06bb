locals {
  env             = get_env("TF_env", "demo")
  prefix          = get_env("TF_prefix", "urw-airports-demo")
  resource_prefix = get_env("TF_resource_prefix", "urw-airports-demo")
  aws_profile     = get_env("TF_aws_profile", "default")
  tf_bucket       = "${local.prefix}-terraform"

  # Environment-specific domains
  domain_name = "demo.urwairports.com"
  preview_domain = "preview-demo.urwairports.com"

  # Environment-specific email recipients
  notification_emails = {
    contact_form    = "<EMAIL>"
    register_form   = "<EMAIL>"
    csv_export      = "<EMAIL>"
  }
}
