#------------------------------------------------------------------------------------
# Create SSM parameters
#------------------------------------------------------------------------------------
resource "aws_ssm_parameter" "lambda" {
  for_each = {
    function_url = aws_lambda_function_url.function_url[0].function_url
  }
  name      = "${var.ssm_parameter_path_prefix}/lambda/contact-form/${each.key}"
  type      = "SecureString"
  value     = each.value
  key_id    = "alias/aws/ssm"
  overwrite = true
}
