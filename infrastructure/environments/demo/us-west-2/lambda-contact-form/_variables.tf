variable "contact_response_dyanmodb_table" {
  description = "Contact response dyanamodb table"
  type        = string
}

variable "opportunity_response_dyanmodb_table" {
  description = "Opportunity response dyanamodb table"
  type        = string
}

variable "ssm_parameter_path_prefix" {
  description = "AWS Parameter store parameter path prefix"
  type        = string
}

variable "ses_domain_identity" {
  description = "SES domain identity"
  type        = string
}

variable "source_email" {
  description = "Source email"
  type        = string
}
