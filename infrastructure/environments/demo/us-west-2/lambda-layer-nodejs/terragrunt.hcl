## Include terragrunt config files
include "root" {
  path = find_in_parent_folders()
}

## Local values
locals {
  envvars              = read_terragrunt_config(find_in_parent_folders("env.hcl"))
  globalvars           = read_terragrunt_config(find_in_parent_folders("global.hcl"))
  resource_prefix      = local.envvars.locals.resource_prefix
  env                  = local.envvars.locals.env
  project              = local.globalvars.locals.project
  aws_provider_version = local.globalvars.locals.aws_provider_version
}

## Terraform config
terraform {
  source = "${get_terragrunt_dir()}//."
}

## Provider versions config
generate "versions" {
  path      = "versions.tf"
  if_exists = "overwrite"
  contents  = <<EOF
terraform {
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> ${local.aws_provider_version}"
    }
  }
}
EOF
}

## Inputs
inputs = {
  layer_name              = "${local.resource_prefix}-nodejs-v16"
  lambda_artifacts_bucket = get_env("TF_lambda_artifacts_bucket")
  lambda_layer_file       = get_env("TF_lambda_layer_node_modules_file")
  compatible_runtimes     = ["nodejs22.x"]
}
