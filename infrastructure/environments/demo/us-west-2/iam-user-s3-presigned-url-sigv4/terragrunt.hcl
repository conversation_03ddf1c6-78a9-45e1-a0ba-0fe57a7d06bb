## Include terragrunt config files
include "root" {
  path = find_in_parent_folders()
}

## Local values
locals {
  envvars              = read_terragrunt_config(find_in_parent_folders("env.hcl"))
  globalvars           = read_terragrunt_config(find_in_parent_folders("global.hcl"))
  prefix               = local.envvars.locals.prefix
  env                  = local.envvars.locals.env
  project              = local.globalvars.locals.project
  aws_provider_version = local.globalvars.locals.aws_provider_version
}

## Dependencies
dependency "s3_contactus_responses" {
  config_path                             = "../s3-contactus-responses"
  mock_outputs_allowed_terraform_commands = get_terraform_commands_that_need_input()
  mock_outputs = {
    id  = "id"
    arn = "arn"
  }
  mock_outputs_merge_strategy_with_state = "shallow"
}

## Terraform config
terraform {
  source = "../../../..//modules/iam-user"
}

## Provider versions config
generate "versions" {
  path      = "versions.tf"
  if_exists = "overwrite"
  contents  = <<EOF
terraform {
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> ${local.aws_provider_version}"
    }
  }
}
EOF
}

## Inputs
inputs = {
  name       = "${local.prefix}-s3-presigned-url-sigv4"
  bucket_arn = dependency.s3_contactus_responses.outputs.arn
}
