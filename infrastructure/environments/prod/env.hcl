locals {
  env             = get_env("TF_env", "prod")
  prefix          = get_env("TF_prefix", "urw-airports-prod")
  resource_prefix = get_env("TF_resource_prefix", "urw-airports-prod")
  aws_profile     = get_env("TF_aws_profile", "default")
  tf_bucket       = "${local.prefix}-terraform"

  # Environment-specific domains
  domain_name = "www.urwairports.com"
  root_domain = "urwairports.com"
  preview_domain = "preview.urwairports.com"

  # Environment-specific email recipients
  notification_emails = {
    contact_form  = "<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>"
    register_form = "<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>"
    csv_export    = "<EMAIL>,<EMAIL>"
  }
}