## Include terragrunt config files
include "root" {
  path = find_in_parent_folders()
}

## Local values
locals {
  envvars              = read_terragrunt_config(find_in_parent_folders("env.hcl"))
  globalvars           = read_terragrunt_config(find_in_parent_folders("global.hcl"))
  prefix               = local.envvars.locals.prefix
  env                  = local.envvars.locals.env
  project              = local.globalvars.locals.project
  aws_provider_version = local.globalvars.locals.aws_provider_version
}

## Terraform config
terraform {
  source = "../../../..//modules/dynamodb"
}

## Provider versions config
generate "versions" {
  path      = "versions.tf"
  if_exists = "overwrite"
  contents  = <<EOF
terraform {
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> ${local.aws_provider_version}"
    }
  }
}
EOF
}

inputs = {
  name         = "${local.prefix}-opportunity-response"
  billing_mode = "PAY_PER_REQUEST"
  hash_key     = "propertyId"
  range_key    = "responseId"
  global_secondary_indexes = [
    {
      name            = "propertyId-createdAt-index"
      hash_key        = "propertyId"
      range_key       = "createdAt"
      projection_type = "ALL"
    }
  ]
  attributes = [
    {
      name = "createdAt"
      type = "S"
    },
    {
      name = "propertyId"
      type = "S"
    },
    {
      name = "responseId"
      type = "S"
    }
  ]
}
