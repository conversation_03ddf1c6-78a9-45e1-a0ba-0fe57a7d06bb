#-------------------------------------------------------------------------------
# Data resources
#-------------------------------------------------------------------------------
data "aws_s3_object" "lambda_layer_file" {
  bucket = var.lambda_artifacts_bucket
  key    = var.lambda_layer_file
}

resource "aws_lambda_layer_version" "lambda_layer" {
  layer_name          = var.layer_name
  s3_bucket           = data.aws_s3_object.lambda_layer_file.bucket
  s3_key              = data.aws_s3_object.lambda_layer_file.key
  s3_object_version   = data.aws_s3_object.lambda_layer_file.version_id
  compatible_runtimes = var.compatible_runtimes
}

#-------------------------------------------------------------------------------
# Variables
#-------------------------------------------------------------------------------
variable "compatible_runtimes" {
  description = "Lambda layer compatible runtimes"
  type        = list(any)
}

variable "layer_name" {
  description = "Lambda layer name"
  type        = string
}

variable "lambda_artifacts_bucket" {
  description = "Lambda artifacts bucket name"
  type        = string
}

variable "lambda_layer_file" {
  description = "Lambda layer node js file"
  type        = string
}

#-------------------------------------------------------------------------------
# Outputs
#-------------------------------------------------------------------------------
output "arn" {
  value     = aws_lambda_layer_version.lambda_layer.arn
  sensitive = true
}
