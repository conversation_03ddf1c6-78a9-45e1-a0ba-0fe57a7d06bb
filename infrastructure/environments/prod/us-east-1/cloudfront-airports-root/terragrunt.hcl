## Include terragrunt config files
include "root" {
  path = find_in_parent_folders()
}

## Local values
locals {
  envvars              = read_terragrunt_config(find_in_parent_folders("env.hcl"))
  globalvars           = read_terragrunt_config(find_in_parent_folders("global.hcl"))
  envvars_             = read_terragrunt_config(find_in_parent_folders("env.hcl"))
  prefix               = local.envvars.locals.prefix
  env                  = local.envvars.locals.env
  project              = local.globalvars.locals.project
  aws_provider_version = local.globalvars.locals.aws_provider_version
  aws_profile_         = local.envvars_.locals.aws_profile
  repo_name            = local.globalvars.locals.repo_name
}

## Terraform config
terraform {
  source = "../../../..//modules/cloudfront"
}

## Provider versions config
generate "versions" {
  path      = "versions.tf"
  if_exists = "overwrite"
  contents  = <<EOF
terraform {
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.50.0"
    }
  }
}
EOF
}

## AWS Provider Config
generate "provider_alias" {
  path      = "provider_alias.tf"
  if_exists = "overwrite"
  contents  = <<EOF
provider "aws" {
  profile = "${local.aws_profile_}"
  region = "us-west-2"
  alias  = "us-west-2"
  default_tags {
    tags = {
      Environment = "${local.env}"
      Owner       = "Terraform"
      RepoName    = "${local.repo_name}"
    }
  }
}
EOF
}

## Dependencies
dependency "s3_airports_root" {
  config_path                             = "../../us-west-2/s3-airports-root"
  mock_outputs_allowed_terraform_commands = get_terraform_commands_that_need_input()
  mock_outputs = {
    website_endpoint = "website_endpoint"
    id               = "id"
  }
  mock_outputs_merge_strategy_with_state = "shallow"
}

dependency "acm_airports" {
  config_path                             = "../acm-airports"
  mock_outputs_allowed_terraform_commands = get_terraform_commands_that_need_input()
  mock_outputs = {
    arn = "arn"
  }
  mock_outputs_merge_strategy_with_state = "shallow"
}

dependency "cloudfront_function_redirect_root_to_www" {
  config_path                             = "../cloudfront-function-redirect-root-to-www"
  mock_outputs_allowed_terraform_commands = get_terraform_commands_that_need_input()
  mock_outputs = {
    arn = "arn"
  }
  mock_outputs_merge_strategy_with_state = "shallow"
}

## Inputs
inputs = {
  origin = {
    domain_name = dependency.s3_airports_root.outputs.regional_domain_name
    origin_id   = dependency.s3_airports_root.outputs.id

    ## for http endpoint
    custom_origin_config = {
      origin_protocol_policy = "http-only"
    }
  }

  domain_aliases = ["urwairports.com"]
  acm_arn        = dependency.acm_airports.outputs.arn

  # Cloudfront Edge Functions
  function_association = [
    {
      event_type   = "viewer-request"
      function_arn = dependency.cloudfront_function_redirect_root_to_www.outputs.arn
    }
  ]

  response_headers_policy_id = "f7a7060e-4641-421c-982e-fe57f5b59d85" # Policy created by URW IT Security

  ## Cloud function
  redirect_domain = "www.urwairports.com"
}
