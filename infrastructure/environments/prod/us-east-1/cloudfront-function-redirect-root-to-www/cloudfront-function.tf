#--------------------------------------------------------------------------------
# Cloudfront Function for Redirection traffic | root --> www
#--------------------------------------------------------------------------------
resource "aws_cloudfront_function" "root_to_www_redirection" {
  name    = var.name
  runtime = "cloudfront-js-2.0"
  comment = var.comment
  publish = true
  code    = <<EOF
// Convert object to query string
function objectToQueryString(obj) {
    return Object.keys(obj)
        .map(key => encodeURIComponent(key) + '=' + encodeURIComponent(obj[key].value))
        .join('&');
}

function handler(event) {
    const request = event.request;
    console.log(request);
    const uri = request.uri;
    const requestQueryString= objectToQueryString(request.querystring);
    // Check requeststring is not empty and set final string accordingly 
    const querystring = requestQueryString == '' ? '' : `?$${requestQueryString}`;
    
    var response = {
        statusCode: 301,
        statusDescription: 'Moved Permanently',
        headers: { "location": { "value": `https://${var.redirect_domain}$${uri}$${querystring}` } }
    }
    return response;
}
EOF
}

#--------------------------------------------------------------------------------
# Variable
#--------------------------------------------------------------------------------
variable "redirect_domain" {
  description = "Redirection domain"
  type        = string
}

variable "name" {
  description = "Function name"
  type        = string
}

variable "comment" {
  description = "Function comment"
  type        = string
}

#--------------------------------------------------------------------------------
# Outputs
#--------------------------------------------------------------------------------
output "arn" {
  value = aws_cloudfront_function.root_to_www_redirection.arn
}