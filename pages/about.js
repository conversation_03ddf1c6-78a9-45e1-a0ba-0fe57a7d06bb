import AboutUsPage from "../src/pages/About";
import Header from "../src/components/Layout/Header";
import Footer from "../src/components/Layout/Footer";
import ContentfulApi from "../contentfulApi/contentfulApi";
import RegisterNow from "../src/components/Contact/Register";
import HeadMeta from "../src/components/Common/HeadMeta";
const AboutUs = ({ aboutHeroImage, latestNews, highlights, letsDoBusiness, footerLinks, aboutPageCarousel, aboutPageSection, meta }) => {
    const metaDetail = { ...{ ...meta.items } }[0];
    metaDetail.items = { 0: { ...metaDetail, canonicalUrl: `${process.env.NEXT_PUBLIC_SITE_URL}/about/` } };

    return (
        <>
            <Header />
            <HeadMeta meta={metaDetail} />
            <AboutUsPage aboutHeroImage={aboutHeroImage} latestNews={latestNews} letsDoBusiness={letsDoBusiness} highlights={highlights} aboutPageCarousel={aboutPageCarousel} aboutPageSection={aboutPageSection} />
            <Footer footerLinks={footerLinks} />
            <RegisterNow />
        </>
    );
};

export default AboutUs;

const getStaticProps = async (context) => {
    const isPreviewMode = context.preview || false;

    const aboutHeroImage = await ContentfulApi.GetAboutPageHeroImage(isPreviewMode);
    const latestNews = await ContentfulApi.GetLatestNewsAndStories(isPreviewMode);
    const letsDoBusiness = await ContentfulApi.GetAboutPageLetsDoBusiness(isPreviewMode);
    const footerLinks = await ContentfulApi.GetPortfolioFooterLinks(isPreviewMode);
    const highlights = await ContentfulApi.GetHomePageHighlights(isPreviewMode);
    const aboutPageCarousel = await ContentfulApi.GetAboutPageCarousel(isPreviewMode);
    const aboutPageSection = await ContentfulApi.GetAboutPageSection(isPreviewMode);
    const meta = await ContentfulApi.GetAboutPageSEOMeta(isPreviewMode)
    return {
        props: {
            aboutHeroImage,
            latestNews,
            highlights,
            letsDoBusiness,
            footerLinks,
            aboutPageCarousel,
            aboutPageSection,
            isPreviewMode,
            meta
        },
    };
};

export { getStaticProps };
