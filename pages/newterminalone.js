import PortfolioDetailsPage from "../src/pages/PortfolioDetails";
import Header from "../src/components/Layout/Header";
import Footer from "../src/components/Layout/Footer";
import ContentfulApi from "../contentfulApi/contentfulApi";
import HeadMeta from "../src/components/Common/HeadMeta";
import RegisterNow from "../src/components/Contact/Register";

const NewTerminalOne = ({ portfolioDetail, footerLinks, eventNews }) => {
    const meta = {
        items: [
            {
                title: portfolioDetail?.items[0]?.metaTitle || "",
                description: portfolioDetail?.items[0]?.metaDescription || "",
                canonicalUrl: `${process.env.NEXT_PUBLIC_SITE_URL}/portfolio/${`${portfolioDetail?.items[0]?.slug}/` || ""}`
            },
        ],
    };
    return (
        <>
            <Header />
            <HeadMeta meta={meta} />
            <PortfolioDetailsPage portfolioDetail={portfolioDetail} eventNews={eventNews} />
            <Footer footerLinks={footerLinks} />
            <RegisterNow />
        </>
    );
}


export default NewTerminalOne;


const getStaticProps = async (context) => {
    const isPreviewMode = context.preview || false;

    const portfolioDetail = await ContentfulApi.GetPortfolioDetailsBySlug("johnf-kennedy-international-airport-t1", isPreviewMode);
    const footerLinks = await ContentfulApi.GetPortfolioFooterLinks(isPreviewMode);
    const eventNews = await ContentfulApi.GetEventNews(isPreviewMode);

    return {
        props: {
            portfolioDetail,
            footerLinks,
            isPreviewMode,
            eventNews,
        },
    };
};

export { getStaticProps };
