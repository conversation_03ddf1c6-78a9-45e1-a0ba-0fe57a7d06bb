import "../styles/layout.scss";
import RegistrationProvider from "../context/Registration/RegistrationProvider";
const { ToastContainer } = require("react-toastify");
import "react-toastify/dist/ReactToastify.css";
import PreviewBanner from "../src/components/Common/PreviewBanner";
import TagManager from "react-gtm-module";
import { useEffect } from "react";
const MyApp = ({ Component, pageProps }) => {
  const tagManagerArgs = {
    gtmId: process.env.NEXT_PUBLIC_GTM_ID,
  };

  useEffect(() => {
    document.documentElement.lang = "en";
    TagManager.initialize(tagManagerArgs);
  }, []);
  return (
    <>
      {pageProps.isPreviewMode && <PreviewBanner />}
      <RegistrationProvider>
        <Component {...pageProps} />
        <ToastContainer position="top-right" hideProgressBar={true} newestOnTop={false} draggable={false} pauseOnVisibilityChange closeOnClick pauseOnHover />
      </RegistrationProvider>
    </>
  );
};

export default MyApp;
