import PortfolioDetailsPage from "../../src/pages/PortfolioDetails";
import Header from "../../src/components/Layout/Header";
import Footer from "../../src/components/Layout/Footer";
import ContentfulApi from "../../contentfulApi/contentfulApi";
import HeadMeta from "../../src/components/Common/HeadMeta";
import RegisterNow from "../../src/components/Contact/Register";
const PortfolioDetails = ({ portfolioDetail, footerLinks, eventNews, terminalOnenews }) => {
  const meta = {
    items: [
      {
        title: portfolioDetail?.items[0]?.metaTitle || "",
        description: portfolioDetail?.items[0]?.metaDescription || "",
        canonicalUrl: `${process.env.NEXT_PUBLIC_SITE_URL}/portfolio/${`${portfolioDetail?.items[0]?.slug}/` || ""}`
      },
    ],
  };
  return (
    <>
      <Header />
      <HeadMeta meta={meta} />
      <PortfolioDetailsPage portfolioDetail={portfolioDetail} eventNews={eventNews} terminalOnenews={terminalOnenews} />
      <Footer footerLinks={footerLinks} />
      <RegisterNow />
    </>
  );
};

export default PortfolioDetails;

const getStaticPaths = async () => {
  const portfolio = await ContentfulApi.GetAllPortfolioSlugs();
  const paths = [];

  portfolio?.items?.map((item) => {
    return paths.push({ params: { details: item.slug } });
  });

  return {
    paths,
    fallback: false,
  };
};

const getStaticProps = async (context) => {
  const isPreviewMode = context.preview || false;
  const portfolioDetailSlug = context.params.details;

  const portfolioDetail = await ContentfulApi.GetPortfolioDetailsBySlug(portfolioDetailSlug, isPreviewMode);
  const footerLinks = await ContentfulApi.GetPortfolioFooterLinks(isPreviewMode);
  const eventNews = await ContentfulApi.GetEventNews(isPreviewMode);
  const terminalOnenews = await ContentfulApi.GetTerminalOneNews(isPreviewMode);


  return {
    props: {
      portfolioDetail,
      footerLinks,
      isPreviewMode,
      eventNews,
      terminalOnenews
    },
  };
};

export { getStaticPaths, getStaticProps };

