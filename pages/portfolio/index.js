import Portfolio from "../../src/pages/Portfolio";
import Header from "../../src/components/Layout/Header";
import Footer from "../../src/components/Layout/Footer";
import ContentfulApi from "../../contentfulApi/contentfulApi";
import HeadMeta from "../../src/components/Common/HeadMeta";
import RegisterNow from "../../src/components/Contact/Register";
const PortfolioPage = ({
  portfolioPageLetsDoBusiness,
  portFolioPageHeroImage,
  portFolioCards,
  portFolioAdditionalExperience,
  portfolioCardWithLink,
  footerLinks,
  meta
}) => {
  const metaDetail = { ...{ ...meta.items } }[0];
  metaDetail.items = { 0: { ...metaDetail, canonicalUrl: `${process.env.NEXT_PUBLIC_SITE_URL}/portfolio/` } };

  return (
    <>
      <Header />
      <HeadMeta meta={metaDetail} />
      <Portfolio
        portfolioPageLetsDoBusiness={portfolioPageLetsDoBusiness}
        portFolioPageHeroImage={portFolioPageHeroImage}
        portFolioCards={portFolioCards}
        portFolioAdditionalExperience={portFolioAdditionalExperience}
        portfolioCardWithLink={portfolioCardWithLink}
      />
      <Footer footerLinks={footerLinks} />
      <RegisterNow />
    </>
  );
};

export default PortfolioPage;

const getStaticProps = async (context) => {
  const isPreviewMode = context.preview || false;

  const portfolioPageLetsDoBusiness = await ContentfulApi.GetPortfolioPageLetsDoBusiness(isPreviewMode);
  const portFolioPageHeroImage = await ContentfulApi.GetPortfolioPageHeroImage(isPreviewMode);
  const portFolioCards = await ContentfulApi.GetPortFolioPageCards(isPreviewMode);
  const portFolioAdditionalExperience = await ContentfulApi.GetHomePortfolioPageAdditionalExperience(isPreviewMode);
  const portfolioCardWithLink = await ContentfulApi.GetPortfolioPageWithVideoOrImage(isPreviewMode);
  const footerLinks = await ContentfulApi.GetPortfolioFooterLinks(isPreviewMode);
  const meta = await ContentfulApi.GetPortfolioPageSEOMeta(isPreviewMode);
  return {
    props: {
      portfolioPageLetsDoBusiness,
      portFolioPageHeroImage,
      portFolioCards,
      portFolioAdditionalExperience,
      portfolioCardWithLink,
      footerLinks,
      isPreviewMode,
      meta
    },
  };
};

export { getStaticProps };
