import ContactUsPage from "../src/pages/Contact";
import Header from "../src/components/Layout/Header";
import Footer from "../src/components/Layout/Footer";
import ContentfulApi from "../contentfulApi/contentfulApi";
import RegisterNow from "../src/components/Contact/Register";
import HeadMeta from "../src/components/Common/HeadMeta";
const ContactPage = ({ contactHeroImage, contactBanner, footerLinks, meta }) => {
  const metaDetail = { ...{ ...meta.items } }[0];
  metaDetail.items = { 0: { ...metaDetail, canonicalUrl: `${process.env.NEXT_PUBLIC_SITE_URL}/connect/` } };

  return (
    <>
      <Header />
      <HeadMeta meta={metaDetail} />
      <ContactUsPage contactHeroImage={contactHeroImage} contactBanner={contactBanner} />
      <Footer footerLinks={footerLinks} />
      <RegisterNow />
    </>
  );
};

export default ContactPage;

const getStaticProps = async (context) => {
  const isPreviewMode = context.preview || false;

  const contactHeroImage = await ContentfulApi.GetContactPageHeroImage(isPreviewMode);
  const contactBanner = await ContentfulApi.GetContactPageBanner(isPreviewMode);
  const footerLinks = await ContentfulApi.GetPortfolioFooterLinks(isPreviewMode);
  const meta = await ContentfulApi.GetContactPageSEOMeta(isPreviewMode);

  return {
    props: {
      contactHeroImage,
      contactBanner,
      footerLinks,
      isPreviewMode,
      meta
    },
  };
};

export { getStaticProps };
