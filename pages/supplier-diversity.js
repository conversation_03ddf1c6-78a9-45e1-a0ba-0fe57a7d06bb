import Header from "../src/components/Layout/Header";
import Footer from "../src/components/Layout/Footer";
import RegisterNow from "../src/components/Contact/Register";
import ContentfulApi from "../contentfulApi/contentfulApi";
import { SupplierDiversityPage } from "../src/pages/SupplierDiverity";
import HeadMeta from "../src/components/Common/HeadMeta";


const SupplierDiversity = ({ footerLinks, supplierPageDetails }) => {

    const metaDetail = { title: supplierPageDetails?.items?.[0]?.seoTitle, description: supplierPageDetails?.items?.[0]?.seoDescription };
    metaDetail.items = { 0: { ...metaDetail, canonicalUrl: `${process.env.NEXT_PUBLIC_SITE_URL}/supplier-diversity/` } };

    return (
        <>
            <Header />
            <HeadMeta meta={metaDetail} />
            <SupplierDiversityPage supplierPageDetails={supplierPageDetails} />
            <Footer footerLinks={footerLinks} />
            <RegisterNow />
        </>
    )
}

export default SupplierDiversity;

const getStaticProps = async (context) => {

    const isPreviewMode = context.preview || false;

    const footerLinks = await ContentfulApi.GetPortfolioFooterLinks(isPreviewMode);
    const supplierPageDetails = await ContentfulApi.GetSupplierDiversityPage(isPreviewMode);


    return {
        props: {
            footerLinks,
            supplierPageDetails,
            isPreviewMode,
        },
    };

}


export { getStaticProps };
