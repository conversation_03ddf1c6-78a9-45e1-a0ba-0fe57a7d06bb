import CommunityPage from "../src/pages/Community";
import Header from "../src/components/Layout/Header";
import Footer from "../src/components/Layout/Footer";
import ContentfulApi from "../contentfulApi/contentfulApi";
import RegisterNow from "../src/components/Contact/Register";
import HeadMeta from "../src/components/Common/HeadMeta";
const Community = ({ communityHeroImage, latestNews, letsDoBusiness, partners, cardwithImage, whereWeFocusSection, cardWithVideo, footerLinks, meta }) => {
  const metaDetail = { ...{ ...meta.items } }[0];
  metaDetail.items = { 0: { ...metaDetail, canonicalUrl: `${process.env.NEXT_PUBLIC_SITE_URL}/community/` } };

  return (
    <>
      <Header />
      <HeadMeta meta={metaDetail} />
      <CommunityPage
        communityHeroImage={communityHeroImage}
        latestNews={latestNews}
        letsDoBusiness={letsDoBusiness}
        partners={partners}
        cardwithImage={cardwithImage}
        cardWithVideo={cardWithVideo}
        whereWeFocus={whereWeFocusSection}
      />
      <Footer footerLinks={footerLinks} />
      <RegisterNow />
    </>
  );
};

export default Community;

const getStaticProps = async (context) => {
  const isPreviewMode = context.preview || false;

  const communityHeroImage = await ContentfulApi.GetCommunityPageHeroImage(isPreviewMode);
  const latestNews = await ContentfulApi.GetLatestNewsAndStories(isPreviewMode);
  const letsDoBusiness = await ContentfulApi.GetCommunityPageLetsDoBusiness(isPreviewMode);
  const partners = await ContentfulApi.GetCommunityPagePartners(isPreviewMode);
  const cardwithImage = await ContentfulApi.GetCommunityPageCardWithImage(isPreviewMode);
  const whereWeFocusSection = await ContentfulApi.GetCommunityPageWhereWeFocus(isPreviewMode);
  const cardWithVideo = await ContentfulApi.GetCommunityPageCardWithVideo(isPreviewMode);
  const footerLinks = await ContentfulApi.GetPortfolioFooterLinks(isPreviewMode);
  const meta = await ContentfulApi.GetCommunityPageSEOMeta(isPreviewMode)
  return {
    props: {
      communityHeroImage,
      latestNews,
      letsDoBusiness,
      partners,
      cardwithImage,
      whereWeFocusSection,
      cardWithVideo,
      footerLinks,
      isPreviewMode,
      meta
    },
  };
};

export { getStaticProps };
