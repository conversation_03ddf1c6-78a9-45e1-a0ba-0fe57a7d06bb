import Header from "../src/components/Layout/Header";
import Footer from "../src/components/Layout/Footer";
import ContentfulApi from "../contentfulApi/contentfulApi";
import RegisterNow from "../src/components/Contact/Register";

const NotFound = ({footerLinks}) => {
  return (
    <>
      <Header />
      <section className="page-404">
        <div className="img-404">
          <img src="/assets/images/404.png" style={{width: "500px"}} />
        </div>
      </section>
      <Footer footerLinks={footerLinks} />
      <RegisterNow/>
    </>
  );
};

export default NotFound;

const getStaticProps = async (context) => {
  const isPreviewMode = context.preview || false;

  const footerLinks = await ContentfulApi.GetPortfolioFooterLinks(isPreviewMode);
  return {
    props: {
      footerLinks,
      isPreviewMode,
    },
  };
};

export { getStaticProps };
