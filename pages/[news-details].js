import NewsDetailsPage from "../src/pages/NewsDetails";
import Header from "../src/components/Layout/Header";
import Footer from "../src/components/Layout/Footer";
import ContentfulApi from "../contentfulApi/contentfulApi";
import HeadMeta from "../src/components/Common/HeadMeta";
import RegisterNow from "../src/components/Contact/Register";
const NewsDetails = ({ newsDetail, letsDoBusiness, footerLinks }) => {
  const meta = {
    items: [
      {
        title: newsDetail?.items[0]?.metaTitle || "",
        description: newsDetail?.items[0]?.metaDescription || "",
        canonicalUrl: `${process.env.NEXT_PUBLIC_SITE_URL}/${`${newsDetail?.items[0]?.slug}/` || ""}`,
      },
    ],
  };

  return (
    <>
      <Header />
      <HeadMeta meta={meta} metaogImage={newsDetail?.items[0]?.bannerImage?.url} />
      <NewsDetailsPage newsDetail={newsDetail} letsDoBusiness={letsDoBusiness} />
      <Footer footerLinks={footerLinks} />
      <RegisterNow />
    </>
  );
};

export default NewsDetails;

const getStaticPaths = async () => {
  const news = await ContentfulApi.GetAllNews();
  const terminalOneNews = await ContentfulApi.GetTerminalOneNews();
  const eventNews = await ContentfulApi.GetEventNews();

  const allNews = [...(news?.items || []), ...(terminalOneNews?.items || []), ...(eventNews?.items || [])];
  const paths = [];

  allNews?.map((item) => {
    return paths.push({ params: { "news-details": item.slug } });
  });

  return {
    paths,
    fallback: false,
  };
};

const getStaticProps = async (context) => {
  const isPreviewMode = context.preview || false;
  const newsDetailSlug = context.params["news-details"];

  const newsDetail = await ContentfulApi.GetNewsDetailsBySlug(newsDetailSlug, isPreviewMode);
  const letsDoBusiness = await ContentfulApi.GetNewsPageLetsDoBusiness(isPreviewMode);
  const footerLinks = await ContentfulApi.GetPortfolioFooterLinks(isPreviewMode);
  return {
    props: {
      newsDetail,
      letsDoBusiness,
      footerLinks,
      isPreviewMode,
    },
  };
};

export { getStaticPaths, getStaticProps };
