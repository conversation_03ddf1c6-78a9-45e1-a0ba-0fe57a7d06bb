import Header from "../src/components/Layout/Header";
import Footer from "../src/components/Layout/Footer";
import Home from "../src/pages/Home";
import ContentfulApi from "../contentfulApi/contentfulApi";
import RegisterNow from "../src/components/Contact/Register";
import HeadMeta from "../src/components/Common/HeadMeta";
import { useRouter } from "next/router";
import { useEffect } from "react";
const Homepage = ({ homePageHeroImages, homePageCards, homePageHighlights, homePageWhyURW, homePageAwardsAndHonors, latestNewsandStories, homePageLetsDoBusiness, footerLinks, meta }) => {
  const metaDetail = { ...{ ...meta.items } }[0];
  metaDetail.items = { 0: { ...metaDetail, canonicalUrl: `${process.env.NEXT_PUBLIC_SITE_URL}/` } };
  const router = useRouter();
  useEffect(() => {
    if (router.asPath !== "/" && router.asPath !== "/diversity" && router.asPath !== "/diversity/" && router.route === "/" && router.asPath.indexOf("#") === -1) {
      router.push("/404/");
    } else if ((router.asPath === "/diversity" || router.asPath === "/diversity/") && router.route === "/" && router.asPath.indexOf("#") === -1) {
      setTimeout(() => {
        const modalOverlay = document.querySelectorAll(["div.ReactModal__Overlay"]);
        modalOverlay[0]?.classList?.add("ReactModal__FadeIn");
      }, 1000);
    }
  }, [router.asPath, router.route, router.push]);

  return (
    <>
      <Header />
      <HeadMeta meta={metaDetail} />
      <Home
        homePageHeroImages={homePageHeroImages}
        homePageCards={homePageCards}
        homePageHighlights={homePageHighlights}
        homePageWhyURW={homePageWhyURW}
        homePageAwardsAndHonors={homePageAwardsAndHonors}
        latestNewsandStories={latestNewsandStories}
        homePageLetsDoBusiness={homePageLetsDoBusiness}
      />
      <Footer footerLinks={footerLinks} />
      <RegisterNow />
    </>
  );
};

export default Homepage;

const getStaticProps = async (context) => {
  const isPreviewMode = context.preview || false;

  const homePageHeroImages = await ContentfulApi.GetHomePageHeroImages(isPreviewMode);
  const homePageCards = await ContentfulApi.GetHomePageCards(isPreviewMode);
  const homePageHighlights = await ContentfulApi.GetHomePageHighlights(isPreviewMode);
  const homePageWhyURW = await ContentfulApi.GetHomePageWhyUrwSection(isPreviewMode);
  const homePageAwardsAndHonors = await ContentfulApi.GetHomePageAwardsAndHonors(isPreviewMode);
  const latestNewsandStories = await ContentfulApi.GetLatestNewsAndStories(isPreviewMode);
  const homePageLetsDoBusiness = await ContentfulApi.GetHomePageLetsDoBusiness(isPreviewMode);
  const footerLinks = await ContentfulApi.GetPortfolioFooterLinks(isPreviewMode);
  const meta = await ContentfulApi.GetHomePageSEOMeta(isPreviewMode);
  return {
    props: {
      homePageHeroImages,
      homePageCards,
      homePageHighlights,
      homePageWhyURW,
      homePageAwardsAndHonors,
      latestNewsandStories,
      homePageLetsDoBusiness,
      footerLinks,
      isPreviewMode,
      meta,
    },
  };
};

export { getStaticProps };
