import ContentfulApi from "../../contentfulApi/contentfulApi";
export default async function handler(req, res) {
  const { redirectUrl, secret, newsSlug, portfolioSlug } = req.query;

  if (secret != process.env.CONTENTFUL_PREVIEW_SECRET) {
    return res.status(401).json({ message: "Invalid Secret" });
  }

  let url = "/";

  if (!newsSlug && !redirectUrl && !portfolioSlug) {
    return res.status(401).json({ message: "Invalid slug or redirect url." });
  }

  if (redirectUrl && redirectUrl.trim() != "") {
    url = redirectUrl;
  } else if (newsSlug && newsSlug.trim() != "") {
    const news = await ContentfulApi.GetNewsDetailsBySlug(newsSlug, true);

    if (news.items.length == 0) {
      return res.status(401).json({ message: "Invalid slug" });
    }
    url = `/${newsSlug}`;
  } else if (portfolioSlug && portfolioSlug.trim() != "") {
    const portfolio = await ContentfulApi.GetPortfolioDetailsBySlug(portfolioSlug, true);

    if (portfolio.items.length == 0) {
      return res.status(401).json({ message: "Invalid slug" });
    }
    url = `/portfolio/${portfolioSlug}`;
  }

  res.setPreviewData({});

  res.setHeader("Content-Type", "text/html");
  res.write(
    `<!DOCTYPE html><html><head><meta http-equiv="Refresh" content="0; url=${url}" />
    <script>window.location.href = '${url}'</script>
    </head>`
  );
  res.end();
}

// export default async function handler(req, res) {
//   res.setPreviewData({});
//   res.end();
// }
