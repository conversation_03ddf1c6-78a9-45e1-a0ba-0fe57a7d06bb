import SustainabilityPage from "../src/pages/Sustainability";
import Header from "../src/components/Layout/Header";
import Footer from "../src/components/Layout/Footer";
import ContentfulApi from "../contentfulApi/contentfulApi";
import RegisterNow from "../src/components/Contact/Register";
import HeadMeta from "../src/components/Common/HeadMeta";
const Sustainability = ({ communityHeroImage, latestNews, letsDoBusiness, whereWeFocusSection, cardWithVideo, footerLinks, meta }) => {
  const metaDetail = { ...{ ...meta.items } }[0];
  metaDetail.items = { 0: { ...metaDetail, canonicalUrl: `${process.env.NEXT_PUBLIC_SITE_URL}/sustainability/` } };

  return (
    <>
      <Header />
      <HeadMeta meta={metaDetail} />
      <SustainabilityPage
        communityHeroImage={communityHeroImage}
        latestNews={latestNews}
        letsDoBusiness={letsDoBusiness}
        cardWithVideo={cardWithVideo}
        whereWeFocus={whereWeFocusSection}
      />
      <Footer footerLinks={footerLinks} />
      <RegisterNow />
    </>
  );
};

export default Sustainability;

const getStaticProps = async (context) => {
  const isPreviewMode = context.preview || false;

  const isSustainabilityPage = true;

  const communityHeroImage = await ContentfulApi.GetCommunityPageHeroImage(isSustainabilityPage, isPreviewMode);
  const latestNews = await ContentfulApi.GetLatestNewsAndStories(isPreviewMode);
  const letsDoBusiness = await ContentfulApi.GetCommunityPageLetsDoBusiness(isSustainabilityPage, isPreviewMode);
  const whereWeFocusSection = await ContentfulApi.GetCommunityPageWhereWeFocus(isSustainabilityPage, isPreviewMode);
  const cardWithVideo = await ContentfulApi.GetCommunityPageCardWithVideo(isSustainabilityPage, isPreviewMode);
  const footerLinks = await ContentfulApi.GetPortfolioFooterLinks(isPreviewMode);
  const meta = await ContentfulApi.GetCommunityPageSEOMeta(isSustainabilityPage, isPreviewMode)
  return {
    props: {
      communityHeroImage,
      latestNews,
      letsDoBusiness,
      whereWeFocusSection,
      cardWithVideo,
      footerLinks,
      isPreviewMode,
      meta
    },
  };
};

export { getStaticProps };
