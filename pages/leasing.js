import LeasingPage from "../src/pages/Leasing";
import Header from "../src/components/Layout/Header";
import Footer from "../src/components/Layout/Footer";
import ContentfulApi from "../contentfulApi/contentfulApi";
import RegisterNow from "../src/components/Contact/Register";
import HeadMeta from "../src/components/Common/HeadMeta";
const Leasing = ({
  leasingPageHeroImage,
  leasingPageLetsDoBusiness,
  leasingPageCarousel,
  leasingPageFeaturedBrands,
  leasingPageSection,
  leasingPageBanner,
  leasingPageCardWithVideoOrImage,
  footerLinks,
  meta,
}) => {
  const metaDetail = { ...{ ...meta.items } }[0];
  metaDetail.items = { 0: { ...metaDetail, canonicalUrl: `${process.env.NEXT_PUBLIC_SITE_URL}/leasing/` } };

  return (
    <>
      <Header />
      <HeadMeta meta={metaDetail} />
      <LeasingPage
        leasingPageHeroImage={leasingPageHeroImage}
        leasingPageLetsDoBusiness={leasingPageLetsDoBusiness}
        leasingPageCarousel={leasingPageCarousel}
        leasingPageFeaturedBrands={leasingPageFeaturedBrands}
        leasingPageSection={leasingPageSection}
        leasingPageBanner={leasingPageBanner}
        leasingPageCardWithVideoOrImage={leasingPageCardWithVideoOrImage}
      />
      <Footer footerLinks={footerLinks} />
      <RegisterNow />
    </>
  );
};

export default Leasing;

const getStaticProps = async (context) => {
  const isPreviewMode = context.preview || false;

  const leasingPageHeroImage = await ContentfulApi.GetLeasingPageHeroImage(isPreviewMode);
  const leasingPageLetsDoBusiness = await ContentfulApi.GetLeasingPageLetsDoBusiness(isPreviewMode);
  const leasingPageCarousel = await ContentfulApi.GetLeasingPageCarousel(isPreviewMode);
  const leasingPageFeaturedBrands = await ContentfulApi.GetLeasingPageFeaturedBrands(isPreviewMode);
  const leasingPageSection = await ContentfulApi.GetLeasingPageSection(isPreviewMode);
  const leasingPageBanner = await ContentfulApi.GetLeasingPageBanner(isPreviewMode);
  const leasingPageCardWithVideoOrImage = await ContentfulApi.GetLeasingPageCardWithVideoOrImage(isPreviewMode);
  const footerLinks = await ContentfulApi.GetPortfolioFooterLinks(isPreviewMode);
  const meta = await ContentfulApi.GetLeasingPageSEOMeta(isPreviewMode);
  return {
    props: {
      leasingPageHeroImage,
      leasingPageLetsDoBusiness,
      leasingPageCarousel,
      leasingPageFeaturedBrands,
      leasingPageSection,
      leasingPageBanner,
      leasingPageCardWithVideoOrImage,
      footerLinks,
      isPreviewMode,
      meta,
    },
  };
};

export { getStaticProps };
