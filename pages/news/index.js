import Header from "../../src/components/Layout/Header";
import Footer from "../../src/components/Layout/Footer";
import NewsPage from "../../src/pages/News";
import ContentfulApi from "../../contentfulApi/contentfulApi";
import HeadMeta from "../../src/components/Common/HeadMeta";
import RegisterNow from "../../src/components/Contact/Register";
const News = ({ newsPageHeroImage, newsPageAwardsAndHonors, newsPageLetsDoBusiness, latestNewsandStories, news, footerLinks, meta }) => {

  const metaDetail = { ...{ ...meta.items } }[0];
  metaDetail.items = { 0: { ...metaDetail, canonicalUrl: `${process.env.NEXT_PUBLIC_SITE_URL}/news/` } };

  return (
    <>
      <Header />
      <HeadMeta meta={metaDetail} />
      <NewsPage
        newsPageHeroImage={newsPageHeroImage}
        newsPageAwardsAndHonors={newsPageAwardsAndHonors}
        newsPageLetsDoBusiness={newsPageLetsDoBusiness}
        latestNewsandStories={latestNewsandStories}
        news={news}
      />
      <Footer footerLinks={footerLinks} />
      <RegisterNow />
    </>
  );
};

export default News;

const getStaticProps = async (context) => {
  const isPreviewMode = context.preview || false;

  const newsPageHeroImage = await ContentfulApi.GetNewsPageHeroImage(isPreviewMode);
  const newsPageAwardsAndHonors = await ContentfulApi.GetHomePageAwardsAndHonors(isPreviewMode);
  const newsPageLetsDoBusiness = await ContentfulApi.GetNewsPageLetsDoBusiness(isPreviewMode);
  const latestNewsandStories = await ContentfulApi.GetLatestNewsAndStories(isPreviewMode);
  const footerLinks = await ContentfulApi.GetPortfolioFooterLinks(isPreviewMode);
  const meta = await ContentfulApi.GetNewsPageSEOMeta(isPreviewMode);
  const news = await ContentfulApi.GetAllNews(isPreviewMode);
  return {
    props: {
      newsPageHeroImage,
      newsPageAwardsAndHonors,
      newsPageLetsDoBusiness,
      latestNewsandStories,
      news,
      footerLinks,
      isPreviewMode,
      meta
    },
  };
};

export { getStaticProps };
