import Header from "../../src/components/Layout/Header";
import Footer from "../../src/components/Layout/Footer";
import NewsPage from "../../src/pages/News";
import ContentfulApi from "../../contentfulApi/contentfulApi";
import HeadMeta from "../../src/components/Common/HeadMeta";
import RegisterNow from "../../src/components/Contact/Register";
const News = ({ newsPageHeroImage, newsPageAwardsAndHonors, newsPageLetsDoBusiness, latestNewsandStories, news, footerLinks, meta, currentPage }) => {
  const metaDetail = { ...{ ...meta.items } }[0];
  metaDetail.items = { 0: { ...metaDetail, canonicalUrl: `${process.env.NEXT_PUBLIC_SITE_URL}/news/` } };

  return (
    <>
      <Header />
      <HeadMeta meta={metaDetail} />
      <NewsPage
        newsPageHeroImage={newsPageHeroImage}
        newsPageAwardsAndHonors={newsPageAwardsAndHonors}
        newsPageLetsDoBusiness={newsPageLetsDoBusiness}
        latestNewsandStories={latestNewsandStories}
        news={news}
        currentPage={currentPage}
      />
      <Footer footerLinks={footerLinks} />
      <RegisterNow />
    </>
  );
};

export default News;

const getStaticProps = async (context) => {
  const newsPageMap = {
    "new-york": "NEW YORK",
    chicago: "CHICAGO",
    "los-angeles": "LOS ANGELES",
    "advance-network": "Advance Network",
    "sustainable-experience": "Sustainable Experience",
    "environment": "Environment"

  };
  const currentPage = context.params["news-categories"];
  const isPreviewMode = context.preview || false;

  const newsPageHeroImage = await ContentfulApi.GetNewsPageHeroImage(isPreviewMode);
  const newsPageAwardsAndHonors = await ContentfulApi.GetHomePageAwardsAndHonors(isPreviewMode);
  const newsPageLetsDoBusiness = await ContentfulApi.GetNewsPageLetsDoBusiness(isPreviewMode);
  const latestNewsandStories = await ContentfulApi.GetLatestNewsAndStories(isPreviewMode);
  const footerLinks = await ContentfulApi.GetPortfolioFooterLinks(isPreviewMode);
  const meta = await ContentfulApi.GetNewsPageSEOMeta(isPreviewMode);
  let news;
  if (currentPage === "advance-network" || currentPage === "environment" || currentPage === "sustainable-experience") {
    news = await ContentfulApi.GetNewsByTags(newsPageMap[currentPage], isPreviewMode);
  } else {
    news = await ContentfulApi.GetAllNewsByCategory(newsPageMap[currentPage], isPreviewMode);
  }
  return {
    props: {
      newsPageHeroImage,
      newsPageAwardsAndHonors,
      newsPageLetsDoBusiness,
      latestNewsandStories,
      news,
      footerLinks,
      isPreviewMode,
      meta,
      currentPage
    },
  };
};

const getStaticPaths = async () => {
  const newsCategories = ["new-york", "chicago", "los-angeles", "advance-network", "sustainable-experience", "environment"];
  const paths = [];

  newsCategories.map((item) => {
    return paths.push({ params: { "news-categories": item } });
  });

  return {
    paths,
    fallback: false,
  };
};

export { getStaticProps, getStaticPaths };
